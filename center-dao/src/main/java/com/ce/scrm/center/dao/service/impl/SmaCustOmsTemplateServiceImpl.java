package com.ce.scrm.center.dao.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.center.dao.entity.CustOmsTemplate;
import com.ce.scrm.center.dao.entity.TurnoverCustOfOldCluePageQuery;
import com.ce.scrm.center.dao.mapper.SmaCustOmsTemplateMapper;
import com.ce.scrm.center.dao.service.SmaCustOmsTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description CustOmsTemplate service
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SmaCustOmsTemplateServiceImpl extends ServiceImpl<SmaCustOmsTemplateMapper, CustOmsTemplate>
	implements SmaCustOmsTemplateService {

    private final SmaCustOmsTemplateMapper smaCustOmsTemplateMapper;

	/**
	 * 成交客户-老客户线索跟进
	 * @param condition 查询条件
	 * @return 分页数据
	 */
	@Override
	public Page<CustOmsTemplate> queryFollowUpClueOfOldCust(TurnoverCustOfOldCluePageQuery condition) {
		return smaCustOmsTemplateMapper.queryFollowUpClueOfOldCust(new Page<>(condition.getPageNum(), condition.getPageSize()), condition);
	}
}
