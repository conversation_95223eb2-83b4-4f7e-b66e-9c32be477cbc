package com.ce.scrm.center.service.business.abm;

import cn.ce.cesupport.enums.CustomerCreateWayEnum;
import cn.ce.cesupport.enums.CustomerPresentStageEnum;
import cn.ce.cesupport.enums.abm.LeadsImportSourceEnum;
import cn.ce.cesupport.framework.base.enums.State;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.dao.entity.CustomerTemplateImportDto;
import com.ce.scrm.center.dao.entity.PotentialCustomerMarketingRules;
import com.ce.scrm.center.dao.service.PotentialCustomerMarketingRulesService;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmCustomerTemplateErrorExcel;
import com.ce.scrm.center.service.business.entity.dto.abm.AbmCustomerTemplateExcel;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsAddDto;
import com.ce.scrm.center.service.business.entity.dto.abm.CustomerLeadsImportOrDistributeDto;
import com.ce.scrm.center.service.business.entity.response.BusinessResult;
import com.ce.scrm.center.service.third.entity.dto.customer.CustomerContactPersonThirdDto;
import com.ce.scrm.center.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.center.service.third.entity.view.CustomerAddThirdView;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.BigDataThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerLinkmanThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerTagsThirdService;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.service.utils.BeanCopyUtils;
import com.ce.scrm.center.service.utils.FileUploadUtil;
import com.ce.scrm.center.service.utils.UUIDGenerator;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerTagRelaCreateDubboDto;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description 导入客户相关业务
 * <AUTHOR>
 * @Date 2025-07-14 09:56
 */
@Slf4j
@Service
public class CustomerImportBusiness {
	@Resource
	private CustomerThirdService customerThirdService;
	@Value("${file.upload.dir}")
	private String uploadDir;

	@Value("${file.upload.url}")
	private String uploadUrl;
	@Resource
	private CustomerLeadsBusiness customerLeadsBusiness;
	@Resource
	private PotentialCustomerMarketingRulesService leadsConfigService;
	@Resource
	private BigDataThirdService bigDataThirdService;
	@Resource
	private CustomerLinkmanThirdService customerLinkmanThirdService;
	@Autowired
	private CustomerTagsThirdService customerTagsThirdService;

	private static final String CUST_TAG_RELA_OPERTOR = "1024import";


	/**
	 * 导入客户、leads标签、下发
	 * @param empId 当前登录员工id
	 * @param validSuccessList 需要导入的客户列表和每个客户对应的标签信息
	 * @return URL，有导入失败数据的URL
	 */
	public String importCustomerAndTags(String empId, List<AbmCustomerTemplateExcel> validSuccessList) {
		return importCustomerAndTags(empId, validSuccessList, null);
	}

	/**
	 * 导入客户并和客户标签关系
	 * @param empId 当前登录员工id
	 * @param validSuccessList 需要导入的客户列表和每个客户对应的标签信息
	 * @param validationFailedList 格式校验失败的记录列表（可选）
	 * @return URL，有导入失败数据的URL
	 */
	public String importCustomerAndTags(String empId, List<AbmCustomerTemplateExcel> validSuccessList, List<AbmCustomerTemplateErrorExcel> validationFailedList) {

		Map<AbmCustomerTemplateExcel, String> failedImportMap = new HashMap<>();
		// 针对校验成功的数据开始导入客户库
		List<CustomerTemplateImportDto> importDtos = new ArrayList<>();
		Set<CustomerTagRelaCreateDubboDto> addTagThirdDtos = new HashSet<>();
		List<Long> leadsIds = new ArrayList<>();
		for (AbmCustomerTemplateExcel dto : validSuccessList) {
			CustomerAddThirdView view = new CustomerAddThirdView();
			String customerId = "";
			try {
				// presentStage客户当前阶段; createWay创建方式
				Optional<CustomerDataThirdView> custExist = customerThirdService.getCustomerDataByCustomerName(dto.getCustomerName());
				if (custExist.isPresent()) {
					CustomerDataThirdView customerDataThirdView = custExist.get();
					customerId = customerDataThirdView.getCustomerId();
				} else {
					view = customerThirdService.addCustomer(null, dto.getCustomerName(), CustomerPresentStageEnum.CLUE.getCode(), CustomerCreateWayEnum.KJ_LEADS.getCode(), empId);
					customerId = view.getCustomerId();
				}
				if (StringUtils.isBlank(customerId) || StringUtils.isNotBlank(view.getErrMsg())) {
					log.warn("导入客户失败，客户名称={}，失败原因={}", dto.getCustomerName(), view.getErrMsg());
					failedImportMap.put(dto, "导入客户失败，客户名称=" + dto.getCustomerName() + "，失败原因=" + view.getErrMsg());
				} else { // 创建联系人打KP
					CustomerContactPersonThirdDto customerContactPersonThirdDto = new CustomerContactPersonThirdDto();
					customerContactPersonThirdDto.setCustomerId(customerId);
					customerContactPersonThirdDto.setOperator(empId);
					customerContactPersonThirdDto.setPhone(dto.getPhoneNumber());
					customerContactPersonThirdDto.setContactPersonName(dto.getLegalPersonName());
					String contactPersonId = customerLinkmanThirdService.addContractPerson(customerContactPersonThirdDto);
					log.info("添加联系人成功, contactPersonId={}", contactPersonId);
				}
			} catch (Exception e) {
				log.error("导入客户 [{}] 失败，异常信息: {}", dto.getCustomerName(), e.getMessage(), e);
			}
			// 添加leads
			Pair<String, String> leadsCodeErrMsgPair;
			if (StringUtils.isNotBlank(customerId)) {
				leadsCodeErrMsgPair = addLeadsByImportCustomer(dto, customerId, empId, leadsIds);
				String leadsCode = leadsCodeErrMsgPair.getLeft();
				CustomerTemplateImportDto templateImportDto = new CustomerTemplateImportDto();
				templateImportDto.setCustomerId(customerId);
				templateImportDto.setLeadsCode(leadsCode);
				importDtos.add(templateImportDto);
				if (StringUtils.isNotBlank(leadsCodeErrMsgPair.getRight())) {
					failedImportMap.put(dto, "leads导入失败，" + leadsCodeErrMsgPair.getRight());
				}
				// tagCode先从config表找，找不到再去customer_tag表找
				PotentialCustomerMarketingRules one = leadsConfigService.lambdaQuery().eq(PotentialCustomerMarketingRules::getSourceCode, leadsCode).last("limit 1").one();
				CustomerTagRelaCreateDubboDto tagRelaCreateDubboDto = new CustomerTagRelaCreateDubboDto();
				tagRelaCreateDubboDto.setCustomerId(customerId);
				if (one != null) {
					String tagCodeFromConfig = one.getTagCategoryThreeCode();
					String tagCode = StringUtils.isNotBlank(tagCodeFromConfig) ? tagCodeFromConfig :
						customerTagsThirdService.getCustomerTagByCode(one.getTagCategoryThree()).map(CustomerTagDubboView::getTagCode).orElse(StringUtils.EMPTY);
					if (StringUtils.isBlank(tagCode)) {
						log.error("leadsCode={}，没有找到对应的标签code,请检查标签是否存在", leadsCode);
					} else {
						tagRelaCreateDubboDto.setTagCode(tagCode);
						addTagThirdDtos.add(tagRelaCreateDubboDto);
					}
				}

			}
		}
		// 导入标签
		log.info("导入标签, addTagThirdDtos={}", JSON.toJSONString(addTagThirdDtos));
		customerTagsThirdService.addCustomerTagRela(new ArrayList<>(addTagThirdDtos), CUST_TAG_RELA_OPERTOR);
		// leads下发
		CustomerLeadsImportOrDistributeDto distributeDto = new CustomerLeadsImportOrDistributeDto();
		distributeDto.setLeadsImportFrom(LeadsImportSourceEnum.STANDARDIZATION_CUSTOMER_IMPORT.getCode());
		distributeDto.setCustomerTemplateImportDtos(importDtos);
		customerLeadsBusiness.distributeByConfig(distributeDto, leadsIds);
		return buildFailedExcel(failedImportMap, validationFailedList);
	}

	/**
	 * 导入绿化后的客户数据后，添加leads
	 */
	private Pair<String, String> addLeadsByImportCustomer(AbmCustomerTemplateExcel excelRow, String customerId, String opertor, List<Long> leadsIds) {
		String errorMsg = "";
		CustomerLeadsAddDto leads = new CustomerLeadsAddDto();
		String leadsCode = excelRow.getLeadsCode();
		leads.setLeadsCode(leadsCode);
		leads.setCustomerName(excelRow.getCustomerName());
		leads.setCustomerId(customerId);
		leads.setDataFromSource(1);
		PotentialCustomerMarketingRules configRule = leadsConfigService.lambdaQuery().eq(PotentialCustomerMarketingRules::getSourceCode, excelRow.getLeadsCode()).last("limit 1").one();
		if (configRule == null) {
			log.error("导入leads失败，没有根据LeadsCode找到对应的leads配置规则, leadsCode={}", excelRow.getLeadsCode());
		}
		leads.setLeadsType(configRule.getIntentCode() + "-" + configRule.getIntentName());
		leads.setLeadsSource(configRule.getSourceName());
		leads.setLeadsDesc(configRule.getSourceDesc());

		leads.setCreatedId(opertor);
		leads.setMobile(excelRow.getPhoneNumber());
		leads.setEmail(excelRow.getEmail());
		// 四要素
		leads.setAddress(excelRow.getOfficeAddress());
		leads.setMobile(excelRow.getPhoneNumber());
		leads.setLinkmanName(excelRow.getLegalPersonName());
		// 省市区 查skb
		try {
			BigDataCompanyDetail bigDataCustomerInfo = bigDataThirdService.getBigDataCustomerInfo(excelRow.getCustomerName());
			if (bigDataCustomerInfo != null) {
				leads.setProvinceCode(bigDataCustomerInfo.getProvince_code());
				leads.setCityCode(bigDataCustomerInfo.getCity_code());
				leads.setDistrictCode(bigDataCustomerInfo.getDistrict_code());
			}
		} catch (Exception e) {
			log.error("省市区 查skb失败，error={}", e.getMessage());
		}
		try {
			BusinessResult<Long> longBusinessResult = customerLeadsBusiness.addCustomerLead(leads);
			log.info("绿化后导入leads结果={}", JSON.toJSONString(longBusinessResult));
			if (longBusinessResult.checkSuccess()) {
				leadsIds.add(longBusinessResult.getData());
			}
		} catch (Exception e) {
			errorMsg = e.getMessage();
			log.error("绿化后导入leads失败，leadsCode={}, errorMsg={}", leadsCode, errorMsg);
		}
		return Pair.of(leadsCode, errorMsg);
	}


	/**
	 * 构建Excel并上传，最后返回上传成功的URL文件地址
	 * @param importCustAndTagBusinessDtos 导入失败的客户业务数据列表
	 * @param validationFailedList 格式校验失败的记录列表
	 * @return 上传成功的文件URL，如果没有失败数据则返回null
	 * <AUTHOR>
	 */
	private String buildFailedExcel(Map<AbmCustomerTemplateExcel, String> importCustAndTagBusinessDtos, List<AbmCustomerTemplateErrorExcel> validationFailedList) {
		// 检查是否有任何失败数据
		boolean hasBusinessFailed = !CollectionUtils.isEmpty(importCustAndTagBusinessDtos);
		boolean hasValidationFailed = !CollectionUtils.isEmpty(validationFailedList);

		if (!hasBusinessFailed && !hasValidationFailed) {
			log.info("没有任何失败的数据，无需生成Excel文件");
			return null;
		}

		try {
			log.info("开始生成失败数据Excel文件，业务失败数据条数: {}，格式校验失败数据条数: {}",
				hasBusinessFailed ? importCustAndTagBusinessDtos.size() : 0,
				hasValidationFailed ? validationFailedList.size() : 0);

			// 合并所有失败记录
			List<AbmCustomerTemplateErrorExcel> allFailedList = new ArrayList<>();

			// 添加格式校验失败的记录
			if (hasValidationFailed) {
				log.info("添加格式校验失败记录: {}条", validationFailedList.size());
				allFailedList.addAll(validationFailedList);
			}

			// 处理业务处理失败的记录
			if (hasBusinessFailed) {
				log.info("处理业务处理失败记录: {}条", importCustAndTagBusinessDtos.size());
				List<AbmCustomerTemplateExcel> keySetToList = new ArrayList<>(importCustAndTagBusinessDtos.keySet());
				List<AbmCustomerTemplateErrorExcel> businessFailedList = BeanCopyUtils.convertToVoList(keySetToList, AbmCustomerTemplateErrorExcel.class);

				// 错误信息回填到AbmCustomerTemplateErrorExcel
				Map<String, String> errorMsgMap = new HashMap<>();
				for (Map.Entry<AbmCustomerTemplateExcel, String> entry : importCustAndTagBusinessDtos.entrySet()) {
					AbmCustomerTemplateExcel key = entry.getKey();
					String errorMsg = entry.getValue();
					String uniqueId = key.getCustomerName();
					errorMsgMap.put(uniqueId, errorMsg);
				}
				businessFailedList.forEach(item -> {
					String id = item.getCustomerName();
					String msg = errorMsgMap.get(id);
					if (msg != null)
						item.setErrorMsg(msg);
				});

				allFailedList.addAll(businessFailedList);
			}
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

			try {
				// 创建Excel样式策略
				HorizontalCellStyleStrategy styleStrategy = createExcelStyleStrategy();

				// 生成Excel文件到内存，使用合并后的失败记录列表
				EasyExcel.write(outputStream, AbmCustomerTemplateErrorExcel.class)
						.registerWriteHandler(styleStrategy)
						.sheet("导入失败数据")
						.doWrite(allFailedList);

				// 将内存中的Excel数据转换为MultipartFile
				byte[] excelBytes = outputStream.toByteArray();
				String fileName = generateFailedExcelFileName();
				MultipartFile multipartFile = createMultipartFile(fileName, excelBytes);

				// 构建文件上传路径
				SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
				String format = sdf.format(new Date());
				String filePath = uploadDir + "/abmCustImport/" + format + "/";

				// 调用文件上传工具类上传文件
				Map<String, String> rs = FileUploadUtil.fileUpload(
						multipartFile,
						"xlsx",
						20.0,
						filePath
				);

				// 检查上传结果
				if (!State.OK.getCode().equals(rs.get("code"))) {
					log.error("上传导入失败的客户数据Excel文件失败，错误信息: {}", rs.get("msg"));
					throw new RuntimeException("上传失败客户数据Excel文件失败: " + rs.get("msg"));
				}

				// 构建完整的文件访问URL
				String url = uploadUrl + "/abmCustImport/" + format + "/" + rs.get("fileName");
				log.info("导入客户失败的客户链接，上传文件URL: {}", url);

				return url;

			} finally {
				try {
					outputStream.close();
				} catch (IOException e) {
					log.warn("关闭ByteArrayOutputStream异常", e);
				}
			}

		} catch (Exception e) {
			log.error("生成和上传失败客户数据Excel文件异常", e);
			throw new RuntimeException("生成失败客户数据Excel文件失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 创建Excel样式策略
	 * @return Excel样式策略
	 * <AUTHOR>
	 */
	private HorizontalCellStyleStrategy createExcelStyleStrategy() {
		// 表头样式
		WriteCellStyle headWriteCellStyle = new WriteCellStyle();
		headWriteCellStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
		headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
		WriteFont headWriteFont = new WriteFont();
		headWriteFont.setFontHeightInPoints((short) 12);
		headWriteFont.setBold(true);
		headWriteCellStyle.setWriteFont(headWriteFont);

		// 内容样式
		WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
		contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
		contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		contentWriteCellStyle.setWrapped(false);
		WriteFont contentWriteFont = new WriteFont();
		contentWriteFont.setFontHeightInPoints((short) 11);
		contentWriteCellStyle.setWriteFont(contentWriteFont);

		return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
	}

	/**
	 * 生成失败数据Excel文件名
	 * @return 文件名
	 * <AUTHOR>
	 */
	private String generateFailedExcelFileName() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
		String timestamp = sdf.format(new Date());
		String fileId = UUIDGenerator.generator();
		return "客户导入失败数据_" + timestamp + "_" + fileId + ".xlsx";
	}

	/**
	 * 创建MultipartFile对象
	 * @param fileName 文件名
	 * @param content 文件内容
	 * @return MultipartFile对象
	 * <AUTHOR>
	 */
	private MultipartFile createMultipartFile(String fileName, byte[] content) {
		return new MultipartFile() {
			@Override
			public String getName() {
				return "file";
			}

			@Override
			public String getOriginalFilename() {
				return fileName;
			}

			@Override
			public String getContentType() {
				return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
			}

			@Override
			public boolean isEmpty() {
				return content == null || content.length == 0;
			}

			@Override
			public long getSize() {
				return content == null ? 0 : content.length;
			}

			@Override
			public byte[] getBytes() throws IOException {
				return content;
			}

			@Override
			public InputStream getInputStream() throws IOException {
				return new ByteArrayInputStream(content);
			}

			@Override
			public void transferTo(File dest) throws IOException, IllegalStateException {
				try (FileOutputStream fos = new FileOutputStream(dest)) {
					fos.write(content);
				}
			}
		};
	}

}
