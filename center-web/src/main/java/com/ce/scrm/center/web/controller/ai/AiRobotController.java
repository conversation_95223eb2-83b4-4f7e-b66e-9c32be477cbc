package com.ce.scrm.center.web.controller.ai;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.service.constant.ServiceConstant;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.web.util.JsonToMarkdown;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/ai/robot")
public class AiRobotController {

    @Autowired
    private RocketMqOperate rocketMqOperate;

    @PostMapping("/robotNotify")
    public JSONObject robotNotify(@RequestBody(required = false) Map<String, Object> bodyorigin) {
        log.info("收到回调: {}", JSONObject.toJSONString(bodyorigin));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 200);
        jsonObject.put("message", "回调接收成功");
        rocketMqOperate.syncSend(
                ServiceConstant.MqConstant.Topic.CDP_AI_ROBOT_TOPIC,
                JSONObject.toJSONString(bodyorigin)
        );
        return jsonObject;
    }

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build();

    @GetMapping("/getyeji")
    public Object getResult(
            @RequestParam List<String> businessMonths,  // 接收多个月份参数
            @RequestParam List<String> subIds) {        // 接收多个subIds参数

        // 构建表单参数
        FormBody.Builder formBuilder = new FormBody.Builder();
        // 添加所有businessMonths
        for (String month : businessMonths) {
            formBuilder.add("businessMonths", month);
        }
        // 添加所有subIds
        for (String subId : subIds) {
            formBuilder.add("subIds", subId);
        }
        // 构建请求
        Request request = new Request.Builder()
                .url("http://local-gateway.datauns.cn/ce15tvbigscreen/other/api/subTransactionSummary")
                .addHeader("Request-Origion", "SwaggerBootstrapUi")
                .addHeader("accept", "*/*")
                .addHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
                .post(formBuilder.build())
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                return JsonToMarkdown.convert2TmarkDown(response.body().string());
            } else {
                JSONObject errorJson = new JSONObject();
                errorJson.put("code", 500);
                errorJson.put("message", "第三方接口请求失败: " + response.message());
                return errorJson;
            }
        } catch (IOException e) {
            JSONObject errorJson = new JSONObject();
            errorJson.put("code", 500);
            errorJson.put("message", "请求异常: " + e.getMessage());
            return errorJson;
        }
    }


    @GetMapping("/getyejidetail")
    public Object getyejidetail(
            @RequestParam List<String> businessMonths,  // 接收多个月份参数
            @RequestParam List<String> subIds) {        // 接收多个subIds参数
        // 构建表单参数
        FormBody.Builder formBuilder = new FormBody.Builder();
        // 添加所有businessMonths
        for (String month : businessMonths) {
            formBuilder.add("businessMonths", month);
        }
        // 添加所有subIds
        for (String subId : subIds) {
            formBuilder.add("subIds", subId);
        }
        // 构建请求
        Request request = new Request.Builder()
                .url("http://local-gateway.datauns.cn/ce15tvbigscreen/other/api/subProductTransactionSummary")
                .addHeader("Request-Origion", "SwaggerBootstrapUi")
                .addHeader("accept", "*/*")
                .addHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
                .post(formBuilder.build())
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                return JsonToMarkdown.convert2TmarkDown(response.body().string());
            } else {
                JSONObject errorJson = new JSONObject();
                errorJson.put("code", 500);
                errorJson.put("message", "第三方接口请求失败: " + response.message());
                return errorJson;
            }
        } catch (IOException e) {
            JSONObject errorJson = new JSONObject();
            errorJson.put("code", 500);
            errorJson.put("message", "请求异常: " + e.getMessage());
            return errorJson;
        }
    }

}

