package com.ce.scrm.center.web.controller.ai;

import cn.ce.cesupport.enums.YesOrNoEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.AiAccessAuth;
import com.ce.scrm.center.dao.entity.AiChannelConfig;
import com.ce.scrm.center.dao.entity.AiVoiceAnalyze;
import com.ce.scrm.center.dao.entity.CustomsProduct;
import com.ce.scrm.center.dao.service.AiAccessAuthService;
import com.ce.scrm.center.dao.service.AiChannelConfigService;
import com.ce.scrm.center.dao.service.AiVoiceAnalyzeService;
import com.ce.scrm.center.service.business.AiCustCacheBusiness;
import com.ce.scrm.center.service.business.AiSmartBusiness;
import com.ce.scrm.center.service.business.CustomsProductBusiness;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.ai.*;
import com.ce.scrm.center.service.business.entity.dto.AiBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.skb.BigDataCompanyDetailDto;
import com.ce.scrm.center.service.business.entity.view.ai.AiChatLogBusinessView;
import com.ce.scrm.center.service.business.entity.view.ai.AiPromptInfoBusinessView;
import com.ce.scrm.center.service.business.entity.view.ai.AiStarLogBusinessView;
import com.ce.scrm.center.service.cache.AiCustCacheHandler;
import com.ce.scrm.center.service.config.NlpProperties;
import com.ce.scrm.center.service.third.entity.view.CustomerDataThirdView;
import com.ce.scrm.center.service.third.invoke.CustomerThirdService;
import com.ce.scrm.center.support.redis.RedisOperator;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.entity.dto.AiQueryDto;
import com.ce.scrm.center.web.entity.dto.AiQuerySmartDto;
import com.ce.scrm.center.web.entity.dto.PPTKeyReq;
import com.ce.scrm.center.web.entity.dto.ai.*;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.entity.view.AiPromptInfoWebView;
import com.ce.scrm.center.web.entity.view.ai.CustomerInfoByNameWebView;
import com.ce.scrm.center.web.util.gptstreamutil.AiService;
import com.ce.scrm.center.web.util.gptstreamutil.ResponseConstant;
import com.ce.scrm.center.web.util.gptstreamutil.SseEmitterUtils;
import com.ce.scrm.extend.dubbo.api.ICompanyInfoEsDubbo;
import com.ce.scrm.extend.dubbo.entity.view.AiCustCacheDubboView;
import com.ce.scrm.extend.dubbo.entity.view.CompanyInfoDubboView;
import com.ce.scrm.extend.dubbo.entity.view.ExhibitionCompanyKjDubboView;
import com.ce.scrm.extend.dubbo.entity.view.SubVisitSigningView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/2/24 17:28
 */
@Slf4j
@RestController
@RequestMapping("/analysis")
public class AiSmartController {

    @Autowired
    private AiService aiService;
    @Resource
    private AiCustCacheBusiness aiCustCacheBusiness;
    @Resource
    private AiCustCacheHandler aiCustCacheHandler;

    @Resource
    private CustomerThirdService customerThirdService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RedisOperator redisOperator;

    @Resource
    private AiSmartBusiness aiSmartBusiness;

    @Autowired
    private AiChannelConfigService aiChannelConfigService;

    @Autowired
    private AiAccessAuthService aiAccessAuthService;

    private static String prefix = "SCRM:ENTER:AI:";

    public static String prefixPPt = "SCRM:PPT:AI:";


    @Autowired
    private NlpProperties nlpProperties;

    @Autowired
    private EmployeeInfoBusiness employeeInfoBusiness;

    @Autowired
    CustomsProductBusiness customsProductBusiness;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", check = false)
    private ICompanyInfoEsDubbo iCompanyInfoEsDubbo;

    @Autowired
    private AiVoiceAnalyzeService aiVoiceAnalyzeService;

    public static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    private static final Map<Long, List<String>> END_NAME_KEYWORDS = new HashMap<>();
    private static final Map<Long, List<String>> PRODUCT_NAME_KEYWORDS = new HashMap<>();

    static {
        END_NAME_KEYWORDS.put(35L, Arrays.asList("机床"));
        END_NAME_KEYWORDS.put(36L, Arrays.asList("丝网"));
        END_NAME_KEYWORDS.put(40L, Arrays.asList("木业", "胶合板", "板业", "板材厂", "装饰材料", "生态板"));
        END_NAME_KEYWORDS.put(41L, Arrays.asList("电线", "电缆"));
        END_NAME_KEYWORDS.put(43L, Arrays.asList("法兰"));

        PRODUCT_NAME_KEYWORDS.put(35L, Arrays.asList("数控铣床", "机床", "车铣复合机床"));
        PRODUCT_NAME_KEYWORDS.put(36L, Arrays.asList("不锈钢丝网", "护栏网", "过滤网", "轧花网", "电焊网", "尼龙网布", "金属扩张网", "建筑防护网"));
        PRODUCT_NAME_KEYWORDS.put(37L, Arrays.asList("手动工具", "电动工具", "气动工具", "测量工具", "切割工具", "焊接设备", "紧固件", "五金工具", "五金件"));
        PRODUCT_NAME_KEYWORDS.put(38L, Arrays.asList("坚果", "炒货", "肉脯", "肉干", "膨化食品", "糖果", "巧克力", "果脯", "果干", "烘焙", "糕点", "海味即食", "代餐轻食", "卤味制品", "休闲食品"));
        PRODUCT_NAME_KEYWORDS.put(39L, Arrays.asList("轮式拖拉机", "履带拖拉机", "果园专用拖拉机", "拖拉机变速箱总成", "农机具配套", "动力输出轴", "动力输出轴改装", "拖拉机", "农机具", "拖拉机变速箱"));
        PRODUCT_NAME_KEYWORDS.put(40L, Arrays.asList("刨花板", "密度板", "细木工板", "防火板", "三聚氰胺饰面板", "UV涂装板", "生态板基材", "铝塑板", "人造板材"));
        PRODUCT_NAME_KEYWORDS.put(41L, Arrays.asList("电力电缆", "控制电缆", "阻燃电缆", "光伏电缆", "充电桩电缆", "布电线", "光纤光缆", "电缆接头"));
        PRODUCT_NAME_KEYWORDS.put(42L, Arrays.asList("梭织面料", "针织布", "功能性面料", "蕾丝花边", "无纺布", "纱线", "坯布印染"));
        PRODUCT_NAME_KEYWORDS.put(43L, Arrays.asList("不锈钢法兰", "高压法兰", "对焊法兰", "盲板法兰", "法兰管件", "锻钢法兰", "定制异形法兰"));
        PRODUCT_NAME_KEYWORDS.put(44L, Arrays.asList("螺旋焊接钢管", "3PE防腐管", "球墨铸铁管", "管道补偿器", "管道堵漏器", "非开挖顶管", "管道清管设备"));
        PRODUCT_NAME_KEYWORDS.put(45L, Arrays.asList("离心风机", "轴流风机", "工业风扇", "罗茨鼓风机", "螺杆式空压机", "活塞式空压机", "真空泵", "气体压缩机", "通风系统"));
        PRODUCT_NAME_KEYWORDS.put(46L, Arrays.asList("掘进机", "采煤机", "矿用提升机", "矿山破碎机", "振动筛", "洗选设备", "矿用输送带", "井下防爆设备"));
        PRODUCT_NAME_KEYWORDS.put(47L, Arrays.asList("PVC排水管", "PPR给水管", "镀锌钢管", "无缝钢管", "HDPE双壁波纹管", "铝塑复合管", "阳光板", "建筑模板"));
        PRODUCT_NAME_KEYWORDS.put(48L, Arrays.asList("瓦楞纸板生产线", "印刷开槽模切机", "自动粘箱机", "打包捆扎机", "纸箱钉箱机", "联动生产线改造", "纸品成型设备"));
        PRODUCT_NAME_KEYWORDS.put(49L, Arrays.asList("深沟球轴承", "圆锥滚子轴承", "直线轴承", "带座外球面轴承", "机床主轴轴承", "滚珠丝杠轴承"));

        PRODUCT_NAME_KEYWORDS.put(50L, Arrays.asList("电力变压器、高压开关柜、环网柜、断路器、继电保护装置、电力电容器、智能电表、预装式变电站".split("、")));
        PRODUCT_NAME_KEYWORDS.put(51L, Arrays.asList("六轴关节机器人、SCARA机器人、AGV小车、机器人焊接工作站、码垛系统、机器视觉定位、夹爪执行器".split("、")));
        PRODUCT_NAME_KEYWORDS.put(52L, Arrays.asList("离心泵、齿轮泵、化工泵、磁力泵、消防泵、球阀、闸阀、蝶阀、止回阀、安全阀、调节阀、泵阀成套设备".split("、")));
        PRODUCT_NAME_KEYWORDS.put(53L, Arrays.asList("汽车线束、ECU控制器、发动机缸体、车身结构件、电动助力转向系统、电驱动桥、智能座舱模块、电控悬架".split("、")));
        PRODUCT_NAME_KEYWORDS.put(54L, Arrays.asList("刹车片、机油滤清器、雨刮器、车灯总成、火花塞、汽车坐垫、改装轮毂、后视镜、OBD诊断设备".split("、")));
        END_NAME_KEYWORDS.put(54L, Arrays.asList("汽配", "汽车零部件"));
        PRODUCT_NAME_KEYWORDS.put(55L, Arrays.asList("酱香型白酒、浓香型原浆、年份窖藏酒、定制封坛酒、光瓶口粮酒、礼品盒装酒".split("、")));
        END_NAME_KEYWORDS.put(55L, Arrays.asList("酒业"));

        PRODUCT_NAME_KEYWORDS.put(56L, Arrays.asList("冷轧钢板、镀锌板、彩涂板、不锈钢板、铝板、铜排、金属激光切割加工、钣金定制".split("、")));
        PRODUCT_NAME_KEYWORDS.put(57L, Arrays.asList("齿轮、精密弹簧、标准紧固件、密封圈、传动链条、联轴器、注塑件加工".split("、")));
        PRODUCT_NAME_KEYWORDS.put(58L, Arrays.asList("电缆分支箱、高压避雷器、复合绝缘子、熔断器、箱式变电站壳体、配电智能终端、故障指示器".split("、")));
        PRODUCT_NAME_KEYWORDS.put(59L, Arrays.asList("TFT-LCD面板、工业显示屏、触摸一体机、车载显示屏、OLED柔性屏、显示驱动IC、背光模组".split("、")));
        PRODUCT_NAME_KEYWORDS.put(60L, Arrays.asList("监护仪、医用超声设备、输液泵、手术床、一次性输液器、骨科植入物、体外诊断设备、康复训练器械".split("、")));
        PRODUCT_NAME_KEYWORDS.put(61L, Arrays.asList("抗体药物开发、CRO/CDMO服务、细胞治疗技术、基因检测试剂盒、临床前研究、分子诊断原料、生物试剂".split("、")));
        END_NAME_KEYWORDS.put(61L, Arrays.asList("医药、制药、药业".split("、")));

        PRODUCT_NAME_KEYWORDS.put(63L, Arrays.asList("抗生素类原料药、维生素类原料药、激素类原料药、心血管系统用药原料药、消化系统用药原料药、原料药".split("、")));
        PRODUCT_NAME_KEYWORDS.put(64L, Arrays.asList("中药材、中药饮片、中成药、中药提取物".split("、")));
        PRODUCT_NAME_KEYWORDS.put(65L, Arrays.asList("门窗五金、幕墙五金、装饰五金、水暖五金、紧固五金".split("、")));
        PRODUCT_NAME_KEYWORDS.put(66L, Arrays.asList("硬质合金刀具、硬质合金模具、硬质合金耐磨零件、硬质合金地质矿山工具".split("、")));
        PRODUCT_NAME_KEYWORDS.put(67L, Arrays.asList("食品包装机、药品包装机、日用品包装机、工业产品包装机、贴体真空包装机、包装机".split("、")));

        PRODUCT_NAME_KEYWORDS.put(70L, Arrays.asList("履带式抛丸机、吊钩式抛丸机、转台式抛丸机、悬链式抛丸机、辊道式抛丸机、抛丸机".split("、")));
        PRODUCT_NAME_KEYWORDS.put(71L, Arrays.asList("橡胶制品、塑料制品".split("、")));
        PRODUCT_NAME_KEYWORDS.put(72L, Arrays.asList("污水处理设备、废气处理设备、固废处理设备、噪声治理设备、资源回收设备".split("、")));
        PRODUCT_NAME_KEYWORDS.put(73L, Arrays.asList("金刚石、立方氮化硼、超硬材料刀具、超硬材料磨具、复合超硬材料".split("、")));
        PRODUCT_NAME_KEYWORDS.put(74L, Arrays.asList("防水卷材、防水涂料、防水密封材料、防水板".split("、")));
        PRODUCT_NAME_KEYWORDS.put(75L, Arrays.asList("厨房小家电、生活小家电、个人护理小家电、小家电".split("、")));
        PRODUCT_NAME_KEYWORDS.put(76L, Arrays.asList("挖掘机、装载机、塔式起重机、混凝土泵车、施工升降机、压路机、旋挖钻机、高空作业平台、路面摊铺机".split("、")));
        PRODUCT_NAME_KEYWORDS.put(77L, Arrays.asList("收割机、播种机、插秧机、植保无人机、饲料粉碎机、粮食烘干机、农用灌溉泵、畜禽养殖设备".split("、")));

        PRODUCT_NAME_KEYWORDS.put(80L, Arrays.asList("建筑铝型材、工业铝型材、装饰铝型材、航空航天用铝型材".split("、")));
        PRODUCT_NAME_KEYWORDS.put(81L, Arrays.asList("磁性材料、永磁材料、钕铁硼磁铁、铁氧体磁体、软磁材料、硅钢片、磁粉、磁性组件、磁传感器".split("、")));
        PRODUCT_NAME_KEYWORDS.put(82L, Arrays.asList("交流伺服电机、直流伺服电机、步进伺服电机".split("、")));

        PRODUCT_NAME_KEYWORDS.put(83L, Arrays.asList("水暖五金、水龙头、阀门、管道配件、卫浴设备、水暖工具".split("、")));
        PRODUCT_NAME_KEYWORDS.put(84L, Arrays.asList("箱包制造、旅行箱、背包、手提包、钱包、电脑包、书包、时尚包".split("、")));
        PRODUCT_NAME_KEYWORDS.put(85L, Arrays.asList("无机化学品、有机溶剂、合成树脂、催化剂、表面活性剂、化学试剂".split("、")));
        PRODUCT_NAME_KEYWORDS.put(86L, Arrays.asList("芯片设计、晶圆代工、半导体封装、MCU微控制器、存储芯片、功率器件、传感器芯片".split("、")));
        PRODUCT_NAME_KEYWORDS.put(87L, Arrays.asList("毛巾、面巾、浴巾、毛巾被、沙滩巾、厨房巾".split("、")));
        PRODUCT_NAME_KEYWORDS.put(88L, Arrays.asList("调味品、酱油、醋、盐、糖、味精、鸡精、花椒、八角、桂皮、香叶、辣椒酱、豆瓣".split("、")));
        PRODUCT_NAME_KEYWORDS.put(89L, Arrays.asList("面包、蛋糕、饼干、糕点、月饼、甜甜圈".split("、")));
        PRODUCT_NAME_KEYWORDS.put(90L, Arrays.asList("粮食清理设备、粮食输送设备、粮食加工设备、油脂加工设备".split("、")));
        PRODUCT_NAME_KEYWORDS.put(91L, Arrays.asList("路灯、路灯灯具、路灯杆、照明控制系统、太阳能路灯组件".split("、")));
        PRODUCT_NAME_KEYWORDS.put(92L, Arrays.asList("显示屏总成、触摸屏模组、摄像头模组、金属外壳、玻璃外壳、锂电池、Type-C接口、天线".split("、")));
        PRODUCT_NAME_KEYWORDS.put(93L, Arrays.asList("裁板锯、推台锯、封边机、砂光机、数控开料机、多排钻、木工车床、榫卯机、四面刨、激光切割机、雕刻机、板材分切机、智能木工机械系统、自动送料装置".split("、")));
        PRODUCT_NAME_KEYWORDS.put(94L, Arrays.asList("商用电磁炉、猛火燃气灶、蒸柜、蒸箱、洗碗机、冷链工作台、商用油烟净化器、和面机、切片机、烘焙设备、智能炒菜机器人、制冷展示柜、餐厨垃圾处理器、不锈钢调理台".split("、")));
        PRODUCT_NAME_KEYWORDS.put(95L, Arrays.asList("联合收割机、插秧机、植保无人机、精密播种机、青贮铡草机、拖拉机、果蔬分选设备、粮食烘干塔、滴灌带生产线、农用传感器、智能灌溉系统".split("、")));
        PRODUCT_NAME_KEYWORDS.put(96L, Arrays.asList("马口铁罐、PET饮料瓶、铝箔餐盒、无菌纸包装、注塑食品盒、吸塑托盘、易拉盖、防盗瓶盖、真空收缩袋、硅胶折叠餐盒".split("、")));
        PRODUCT_NAME_KEYWORDS.put(97L, Arrays.asList("UHT灭菌奶、发酵酸奶、婴幼儿配方奶粉、奶酪、黄油、乳清蛋白粉、稀奶油、冷冻甜品、乳糖酶牛奶、益生菌乳饮料".split("、")));
        PRODUCT_NAME_KEYWORDS.put(98L, Arrays.asList("复合包装膜、自立袋、蒸煮袋、铝塑药包膜、重包装FFS膜、抗静电屏蔽袋、自动包装卷膜、易撕盖膜、真空镀铝膜、气调保鲜包装".split("、")));
        PRODUCT_NAME_KEYWORDS.put(99L, Arrays.asList("钛合金骨科植入物、可吸收缝合线、心脏支架、医用硅凝胶、义齿基托树脂、基因检测耗材、细胞培养瓶、人工关节涂层材料、胶原蛋白敷料、免疫诊断微球".split("、")));
        PRODUCT_NAME_KEYWORDS.put(100L, Arrays.asList("工程塑料、碳纤维预浸料、聚氨酯发泡材料、PVDF氟膜、改性PP粒料、热塑性复合材料芳纶蜂窝芯材、LCP液晶聚合物、医用级硅胶、可降解PLA片材".split("、")));
        PRODUCT_NAME_KEYWORDS.put(101L, Arrays.asList("高铝耐火砖、镁碳砖、刚玉浇注料、硅质保温板、陶瓷纤维毯、滑板水口、钢包透气砖、焦炉用硅砖、莫来石轻质砖、纳米绝热板、碳化硅耐火制品".split("、")));
        PRODUCT_NAME_KEYWORDS.put(102L, Arrays.asList("数控刀片、硬质合金铣刀、钻头、PCD金刚石刀具、CBN刀具、涂层刀具、丝锥、铰刀、镗刀、球头铣刀、模具电极刀具、PCB铣刀、非标定制刀具、工具磨床".split("、")));
        PRODUCT_NAME_KEYWORDS.put(103L, Arrays.asList("门锁、门把手、合页、抽屉滑轨、卫浴挂件、厨房五金、晾衣架、家用手工具、收纳箱、挂钩、地漏、不锈钢餐具、硅胶冰格、重力油壶、防撞角".split("、")));
        PRODUCT_NAME_KEYWORDS.put(104L, Arrays.asList("LCD液晶屏模组、OLED柔性屏、工业触控屏、车载显示屏、拼接屏单元、电子墨水屏、LED背光源、曲面屏、高刷新率电竞屏、工控面板、平板显示面板、透明显示屏、可折叠屏模组".split("、")));
        PRODUCT_NAME_KEYWORDS.put(105L, Arrays.asList("泡椒凤爪、酸豆角、酱黄瓜、榨菜、梅干菜、糖蒜、辣白菜、泡萝卜、笋干、海带丝、卤制香菇、麻辣莴笋、古法酱腌菜、山野盐渍菜".split("、")));
        PRODUCT_NAME_KEYWORDS.put(106L, Arrays.asList("塑料注塑模、压铸模、冲压模、橡胶模、玻璃瓶模具、挤塑模头、压弯成型模、鞋底模具、医疗耗材模、精铸蜡模、多腔高速模、热流道模具、双色模".split("、")));
        PRODUCT_NAME_KEYWORDS.put(107L, Arrays.asList("高低压开关柜、环网柜、变压器、电力金具、绝缘子、避雷器、柱上断路器、电缆分支箱、智能电表、故障指示器、绝缘梯、验电器".split("、")));
        PRODUCT_NAME_KEYWORDS.put(109L, Arrays.asList("光学元件、光电器件、光学仪器、光通信器件、新型显示技术产品".split("、")));
        PRODUCT_NAME_KEYWORDS.put(110L, Arrays.asList("不锈钢板材、不锈钢管材、不锈钢线材、不锈钢棒材、不锈钢".split("、")));
        PRODUCT_NAME_KEYWORDS.put(111L, Arrays.asList("螺栓、螺母、螺钉、螺柱、垫圈、销、铆钉、卡箍、膨胀螺栓、化学锚栓、高强度螺栓、自攻螺钉、挡圈、U型螺栓、风电螺栓、汽车紧固件、不锈钢紧固件、钛合金紧固件".split("、")));
        PRODUCT_NAME_KEYWORDS.put(112L, Arrays.asList("速冻面米制品、速冻调制食品、速冻畜禽肉、速冻水产品、速冻水果、速冻蔬菜、速冻食品".split("、")));
        PRODUCT_NAME_KEYWORDS.put(113L, Arrays.asList("烟花弹、组合烟花、旋转烟花、旋转升空烟花、吐珠烟花、玩具烟花、烟花".split("、")));
        PRODUCT_NAME_KEYWORDS.put(114L, Arrays.asList("中性笔、笔芯、记号笔、荧光笔、文件袋、文件夹、订书机、订书钉、长尾夹、回形针、修正带、固体胶、胶水、笔记本、便签纸、美工刀、桌面收纳盒、磁性白板、财务装订机、定制logo文具、文具".split("、")));
        PRODUCT_NAME_KEYWORDS.put(115L, Arrays.asList("激光切割机、激光打标机、激光焊接机、激光雕刻机、激光清洗机".split("、")));
        PRODUCT_NAME_KEYWORDS.put(116L, Arrays.asList("皮鞋、运动鞋、休闲鞋、靴子、凉鞋、拖鞋".split("、")));
        PRODUCT_NAME_KEYWORDS.put(117L, Arrays.asList("女装、男装、上衣、裤子、裙子、内衣、童装、老年装、职业装、服装".split("、")));
        PRODUCT_NAME_KEYWORDS.put(118L, Arrays.asList("仪器仪表、压力变送器、流量计、温度传感器、工业自动化控制系统、实验室分析仪器".split("、")));
        PRODUCT_NAME_KEYWORDS.put(119L, Arrays.asList("检测仪器、金属探伤仪、环境检测仪、材料试验机、食品安全快速检测仪、RoHS检测设备".split("、")));
        PRODUCT_NAME_KEYWORDS.put(120L, Arrays.asList("涂料、工业防腐涂料、建筑乳胶漆、汽车修补漆、木器漆、船舶涂料、UV固化涂料、防水涂料".split("、")));
        PRODUCT_NAME_KEYWORDS.put(121L, Arrays.asList("母粒、黑色母、功能母粒、彩色母粒、填充母粒、降解母粒、纤维纺丝母粒".split("、")));
        PRODUCT_NAME_KEYWORDS.put(122L, Arrays.asList("酶制剂、糖化酶、蛋白酶、纤维素酶、脂肪酶、食品级凝乳酶、饲料复合酶".split("、")));
        PRODUCT_NAME_KEYWORDS.put(123L, Arrays.asList("玻璃、钢化玻璃、中空节能玻璃、玻璃瓶罐、玻璃器皿、防火玻璃、光伏超白玻璃".split("、")));
        PRODUCT_NAME_KEYWORDS.put(124L, Arrays.asList("混凝土、强商砼、自密实混凝土、发泡轻质混凝土、钢纤维耐火混凝土、预制装配式构件、透水混凝土、彩色艺术地坪、高铁无砟轨道板、海工防腐混凝土".split("、")));
        PRODUCT_NAME_KEYWORDS.put(125L, Arrays.asList("输变电设备、电力变压器、GIS组合电器、电缆分支箱、柱上断路器、铁塔金具、绝缘横担".split("、")));
        PRODUCT_NAME_KEYWORDS.put(126L, Arrays.asList("兽药、抗生素粉剂、宠物疫苗、驱虫药、消毒剂、宠物营养补充剂".split("、")));
        PRODUCT_NAME_KEYWORDS.put(127L, Arrays.asList("活性炭、椰壳活性炭、煤质柱状活性炭、黄金提取活性炭、VOCs废气处理炭、水处理过滤炭、再生活性炭".split("、")));
        PRODUCT_NAME_KEYWORDS.put(128L, Arrays.asList("轮胎、子午线轮胎、工程机械轮胎、实心轮胎、雪地胎、轮胎翻新、高性能赛车胎".split("、")));
        PRODUCT_NAME_KEYWORDS.put(129L, Arrays.asList("体育用品、碳纤维自行车架、健身器械、羽毛球拍、乒乓球台、运动护具、泳池设备".split("、")));
        PRODUCT_NAME_KEYWORDS.put(130L, Arrays.asList("健身器材、史密斯综合训练架、磁控动感单车、可调式哑铃套装、力量训练站、瑜伽垫、智能跑步机".split("、")));
        PRODUCT_NAME_KEYWORDS.put(131L, Arrays.asList("吉他、全单板民谣吉他、电吉他、古典尼龙弦吉他、尤克里里、吉他效果器、调音器".split("、")));
        PRODUCT_NAME_KEYWORDS.put(132L, Arrays.asList("食用菌、干制香菇、冷冻金针菇、即食杏鲍菇脆片、灵芝孢子粉、菌包、食用菌培养料".split("、")));
        PRODUCT_NAME_KEYWORDS.put(133L, Arrays.asList("数控机械、数控雕刻机、PCB钻孔机、激光切割机、多轴联动加工中心、数控弯管机、电火花线切割机".split("、")));
        PRODUCT_NAME_KEYWORDS.put(134L, Arrays.asList("钢球、轴承钢球、不锈钢球、碳钢球、空心球、精密测量用标准球".split("、")));
        PRODUCT_NAME_KEYWORDS.put(135L, Arrays.asList("实木家具、实木餐桌、实木书柜、实木茶台、儿童实木床、实木沙发、实木中式衣柜、原木书桌、整板大板桌、实木餐边柜、实木床屏、实木收纳柜".split("、")));
        PRODUCT_NAME_KEYWORDS.put(136L, Arrays.asList("衣架、浸塑防滑衣架、酒店专用衣架、儿童卡通衣架、服装展示架、智能烘干衣架".split("、")));
        PRODUCT_NAME_KEYWORDS.put(137L, Arrays.asList("轻小型起重设备、手拉葫芦、环链电动葫芦、液压搬运车、墙式旋臂起重机、起重机、轻型龙门架、龙门架、卷扬机".split("、")));
        PRODUCT_NAME_KEYWORDS.put(138L, Arrays.asList("智能制造装备、柔性生产线集成、智能物流AGV系统、MES生产执行系统、机器视觉检测设备".split("、")));
        PRODUCT_NAME_KEYWORDS.put(139L, Arrays.asList("消防设备、消防水炮、自动喷淋系统、消防报警控制器、灭火器、消防应急照明、防火门、消防水泵接合器".split("、")));
        PRODUCT_NAME_KEYWORDS.put(140L, Arrays.asList("工业电炉、中频感应熔炼炉、台车式电阻炉、真空烧结炉、铝合金时效炉、热处理生产线集成".split("、")));
        PRODUCT_NAME_KEYWORDS.put(141L, Arrays.asList("乳品机械、无菌灌装机、巴氏杀菌隧道、CIP清洗系统、均质机、酸奶发酵罐、奶粉喷雾干燥塔".split("、")));
        PRODUCT_NAME_KEYWORDS.put(142L, Arrays.asList("食品机械、自动包子机、果蔬分选机、真空包装机、连续式油炸生产线、冰淇淋凝冻机".split("、")));
        PRODUCT_NAME_KEYWORDS.put(143L, Arrays.asList("食品添加剂、防腐剂、甜味剂、乳化剂、食用色素、增稠剂、营养强化剂".split("、")));
        PRODUCT_NAME_KEYWORDS.put(144L, Arrays.asList("果蔬罐头、黄桃罐头、蘑菇罐头、番茄酱罐头、芦笋罐头、混合水果罐头、低糖型果蔬罐头".split("、")));
        PRODUCT_NAME_KEYWORDS.put(145L, Arrays.asList("健康食品、有机杂粮、低温烘焙坚果、无添加果干、冷榨椰子油、高纤维代餐粉、益生菌酸奶、零卡糖饮料、冻干蔬菜脆、超高压灭菌果汁、植物蛋白棒、亚麻籽粉、藜麦轻食、可溯源鲜食".split("、")));
        PRODUCT_NAME_KEYWORDS.put(146L, Arrays.asList("保健食品、氨糖软骨素、叶黄素酯片、褪黑素软糖、益生菌粉、乳清蛋白粉、纳豆激酶胶囊、灵芝孢子油、猴头菇口服液、胶原蛋白肽粉、蓝莓叶黄素酯、鱼油凝胶糖果、特医食品".split("、")));
    }


    @RequestMapping(value = "/completions/getAnalysisTips/params", method = RequestMethod.POST)
    public WebResult<?> getAiTipsParams(@RequestBody AiQuerySmartDto aiQueryDto) {
        log.info("请求信息:{}", JSONObject.toJSONString(aiQueryDto));
        if (StringUtils.isEmpty(aiQueryDto.getPid()) && StringUtils.isEmpty(aiQueryDto.getCustId())) {
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        String result = aiService.getCustInfo(aiQueryDto);
        if (!StringUtils.hasText(result)) {
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.PARAM_NOT_VALID, errorSseEmitter);
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        } else {
            return WebResult.success(result);
        }
    }


    //@Login
    @RequestMapping(value = "/getAnalysisPromptIdAndTitleList", method = RequestMethod.POST)
    public WebResult<List<AiPromptInfoBusinessView>> getAnalysisPromptIdAndTitleList(@RequestBody QueryAiPromptInfoWebDto queryAiPromptInfoWebDto) {
        EmployeeInfoBusinessDto employeeInfoBusinessDto = employeeInfoBusiness.getEmployeeCheckStateByEmpId(queryAiPromptInfoWebDto.getLoginEmployeeId());
        if (null == employeeInfoBusinessDto) {
            return WebResult.error("500", "登录人信息不存在");
        }
        /**********/
        List<Long> ids = new ArrayList<>();
        try {
            List<String> entNameList = new ArrayList<>();
            String entName = "";
            String pid = queryAiPromptInfoWebDto.getPid();
            String productName = "";
            List<String> industryList = new ArrayList<>();
            if (StringUtils.hasText(queryAiPromptInfoWebDto.getCustId())) {
                Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(queryAiPromptInfoWebDto.getCustId());
                if (customerData.isPresent()) {
                    entNameList.add(customerData.get().getCustomerName());
                    industryList.add(customerData.get().getFirstIndustryName());
                    industryList.add(customerData.get().getSecondIndustryName());
                    if (!StringUtils.hasText(pid)) {
                        pid = customerData.get().getSourceDataId();
                    }
                }
            }
            if (StringUtils.hasText(pid)) {
                BigDataCompanyDetailDto bigDataCompanyDetailDto = aiCustCacheBusiness.assembleCompanyDetail(pid);
                if (null != bigDataCompanyDetailDto) {
                    entNameList.add(bigDataCompanyDetailDto.getEntname());
                    entNameList.addAll(bigDataCompanyDetailDto.getHisotry_names());
                    industryList.add(bigDataCompanyDetailDto.getIndustryL1_desc());
                    industryList.addAll(bigDataCompanyDetailDto.getIndustryL2_desc());
                    industryList.add(bigDataCompanyDetailDto.getIndustryL3_desc());
                    industryList.add(bigDataCompanyDetailDto.getIndustryL4_desc());
                }
                List<CompanyInfoDubboView> companyInfoDubboViewList = iCompanyInfoEsDubbo.listByPid(pid);
                if (!CollectionUtils.isEmpty(companyInfoDubboViewList)) {
                    CompanyInfoDubboView companyInfoDubboView = companyInfoDubboViewList.get(0);
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(companyInfoDubboView.getB2bProduct())) {
                        productName = companyInfoDubboView.getB2bProduct();
                    }
                }
            }
            String endNames = entNameList.stream().filter(StringUtils::hasText).distinct().collect(Collectors.joining(","));
            String industryName = industryList.stream().filter(StringUtils::hasText).distinct().collect(Collectors.joining(","));
            List<Long> ids1 = matchKeywords(endNames, END_NAME_KEYWORDS);
            List<Long> ids2 = matchKeywords(productName, PRODUCT_NAME_KEYWORDS);
            ids.addAll(ids1);
            ids.addAll(ids2);
            ids = ids.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询产品和公司名称异常", e);
        }
        Set<Long> name = END_NAME_KEYWORDS.keySet();
        Set<Long> product = PRODUCT_NAME_KEYWORDS.keySet();
        List<Long> nameList = new ArrayList<>();
        nameList.addAll(name);
        nameList.addAll(product);
        /**********/
        queryAiPromptInfoWebDto.setAreaId(employeeInfoBusinessDto.getAreaId());
        queryAiPromptInfoWebDto.setBuId(employeeInfoBusinessDto.getBuId());
        queryAiPromptInfoWebDto.setDeptId(employeeInfoBusinessDto.getOrgId());
        queryAiPromptInfoWebDto.setSubId(employeeInfoBusinessDto.getSubId());
        String position = employeeInfoBusinessDto.getPosition();
        QueryAiPromptInfoBusinessDto queryAiPromptInfoBusinessDto = BeanUtil.copyProperties(queryAiPromptInfoWebDto, QueryAiPromptInfoBusinessDto.class);
        queryAiPromptInfoBusinessDto.setEmployeeId(queryAiPromptInfoWebDto.getLoginEmployeeId());
        List<AiPromptInfoBusinessView> promptInfoPage = aiSmartBusiness.getAnalysisPromptIdAndNameList(queryAiPromptInfoBusinessDto, position, ids, new HashSet<>(nameList));
        return WebResult.success(promptInfoPage);
    }

    @Login
    @RequestMapping(value = "/getAnalysisPromptInfoPage", method = RequestMethod.POST)
    public WebResult<WebPageInfo<AiPromptInfoWebView>> getAiPromptInfoPage(@RequestBody QueryAiPromptInfoWebDto queryAiPromptInfoWebDto) {
        log.info("当前登录信息:{}", JSONObject.toJSONString(queryAiPromptInfoWebDto));
        QueryAiPromptInfoBusinessDto queryAiPromptInfoBusinessDto = BeanUtil.copyProperties(queryAiPromptInfoWebDto, QueryAiPromptInfoBusinessDto.class);
        queryAiPromptInfoBusinessDto.setCompany(getCompanyEmployeeAiPromote(queryAiPromptInfoWebDto.getLoginEmployeeId()));
        List<String> company = getCompanyEmployeeAiPromote(queryAiPromptInfoWebDto.getLoginEmployeeId());
        if (CollectionUtils.isEmpty(company)) {
            log.warn("当前登录人信息未归属到权限组");
            return WebResult.success(new WebPageInfo<>());
        }
        if (CollectionUtils.isEmpty(queryAiPromptInfoWebDto.getPromptTypeList())) {
            if (null == queryAiPromptInfoWebDto.getPromptType()) {
                queryAiPromptInfoBusinessDto.setPromptTypeList(Arrays.asList(1, 2));
            } else {
                queryAiPromptInfoBusinessDto.setPromptTypeList(Collections.singletonList(queryAiPromptInfoWebDto.getPromptType()));
            }
        }
        Page<AiPromptInfoBusinessView> promptInfoPage = aiSmartBusiness.getAiPromptInfoPage(queryAiPromptInfoBusinessDto, company.contains("master"));
        WebPageInfo<AiPromptInfoWebView> result = WebPageInfo.pageConversion(promptInfoPage, AiPromptInfoWebView.class);
        return WebResult.success(result);
    }

    /***
     * ai 点赞，点low 列表
     * @param aiStarInfoWebDto
     * <AUTHOR>
     * @date 2025/3/13 14:22
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<com.ce.scrm.center.web.entity.response.WebPageInfo < com.ce.scrm.center.service.business.entity.view.ai.AiStarLogBusinessView>>
     **/
    @RequestMapping(value = "/analysisStarLogList", method = RequestMethod.POST)
    public WebResult<WebPageInfo<AiStarLogBusinessView>> analysisStarLogList(@RequestBody AiStarLogWebDto aiStarInfoWebDto) {
        AiStarLogBusinessDto aiStarInfoBusinessDto = BeanUtil.copyProperties(aiStarInfoWebDto, AiStarLogBusinessDto.class);
        Page<AiStarLogBusinessView> promptInfoPage = aiSmartBusiness.analysisStarLogList(aiStarInfoBusinessDto);
        WebPageInfo<AiStarLogBusinessView> result = WebPageInfo.pageConversion(promptInfoPage, AiStarLogBusinessView.class);
        return WebResult.success(result);
    }

    @Login
    @RequestMapping(value = "/addAnalysisPromptInfo", method = RequestMethod.POST)
    public WebResult<Boolean> addAiPromptInfo(@RequestBody @Valid AddAiPromptInfoWebDto addAiPromptInfoWebDto) {
        AddAiPromptInfoBusinessDto addAiPromptInfoBusinessDto = BeanUtil.copyProperties(addAiPromptInfoWebDto, AddAiPromptInfoBusinessDto.class);
        Boolean result = aiSmartBusiness.addAiPromptInfo(addAiPromptInfoBusinessDto);
        return WebResult.success(result);
    }

    @Login
    @RequestMapping(value = "/updateAnalysisPromptInfoById", method = RequestMethod.POST)
    public WebResult<Boolean> updateAiPromptInfoById(@RequestBody @Valid UpdateAiPromptInfoWebDto updateAiPromptInfoWebDto) {
        UpdateAiPromptInfoBusinessDto updateAiPromptInfoBusinessDto = BeanUtil.copyProperties(updateAiPromptInfoWebDto, UpdateAiPromptInfoBusinessDto.class);
        Boolean result = aiSmartBusiness.updateAiPromptInfoById(updateAiPromptInfoBusinessDto);
        return WebResult.success(result);
    }

    @Login
    @RequestMapping(value = "/updateAiPromptStatus", method = RequestMethod.POST)
    public WebResult<Boolean> updateAiPromptStatus(@RequestBody @Valid UpdateAiPromptInfoWebDto updateAiPromptInfoWebDto) {
        UpdateAiPromptInfoBusinessDto updateAiPromptInfoBusinessDto = BeanUtil.copyProperties(updateAiPromptInfoWebDto, UpdateAiPromptInfoBusinessDto.class);
        Boolean result = aiSmartBusiness.updateAiPromptStatus(updateAiPromptInfoBusinessDto);
        return WebResult.success(result);
    }


    @Login
    @RequestMapping(value = "/hasPermissionsForPromote", method = RequestMethod.POST)
    public WebResult<JSONObject> hasPermissionsForPromote(@RequestBody LoginInfo loginInfo) {
        List<String> employeeIds = getAllEmployeeIds();
        log.info("当前可以进入的人信息:{}", JSONObject.toJSONString(employeeIds));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("hasPermissions", employeeIds.contains(loginInfo.getLoginEmployeeId()));
        List<String> strings = this.getCompanyEmployeeAiPromote(loginInfo.getLoginEmployeeId());
        jsonObject.put("isMaster", strings.contains("master"));
        if (strings.contains("master")) {
            strings = strings.stream().filter(T -> !T.equals("master")).collect(Collectors.toList());
        }
        jsonObject.put("companyRole", strings);
        return WebResult.success(jsonObject);
    }

    @RequestMapping(value = "/buildJsonDataAndCdp", method = RequestMethod.POST)
    public WebResult<JSONObject> buildJsonDataAndCdp(@RequestBody AiQueryDto aiQueryDto) {
        AiBusinessDto businessDto = BeanUtil.copyProperties(aiQueryDto, AiBusinessDto.class);
        try {
            JSONObject jsonObjectResult = aiCustCacheBusiness.buildJsonDataAndCdp(businessDto);
            return WebResult.success(jsonObjectResult);
        } catch (Exception e) {
            log.info("buildJsonDataAndCdp,ai获取异常", e);
            return WebResult.error(WebCodeMessageEnum.RPC_EXCEPTION);
        }
    }


    /**
     * 保存更新 配置
     */
    @RequestMapping(value = "/saveAiChannelConfig", method = RequestMethod.POST)
    public WebResult<Boolean> saveAiChannelConfig(@RequestBody AiChannelConfig aiChannelConfig) {
        try {
            boolean result = aiChannelConfigService.saveOrUpdateAiChannelConfig(aiChannelConfig);
            return WebResult.success(result);
        } catch (Exception e) {
            log.warn("更新失败，无可用配置项");
            return WebResult.success(false);
        }
    }

    /**
     * 获取配置列表
     */
    @RequestMapping(value = "/aiChannelConfigList", method = RequestMethod.POST)
    public WebResult<List<AiChannelConfig>> aiChannelConfigList() {
        List<AiChannelConfig> aiChannelConfigList = aiChannelConfigService.lambdaQuery()
                .eq(AiChannelConfig::getDeleteFlag, YesOrNoEnum.NO.getCode())
                .list();
        return WebResult.success(aiChannelConfigList);
    }

    /**
     * 客户分析助手 提示
     */
    @RequestMapping(value = "/tips", method = RequestMethod.POST)
    public WebResult<String> tips() {
        return WebResult.success(nlpProperties.getPromoteGuide());
    }


    /**
     * 获取配置列表
     */
    @RequestMapping(value = "/getQpsCurrent", method = RequestMethod.GET)
    public WebResult<JSONObject> getQpsCurrent() {
        return WebResult.success(aiService.getQpsList());
    }

    /**
     * 获取聊天记录列表
     *
     * @version 1.0.0
     * <AUTHOR>
     */
    @RequestMapping(value = "/getChatLogList", method = RequestMethod.POST)
    public WebResult<WebPageInfo<AiChatLogBusinessView>> getChatLogList(@RequestBody AiChatLogWebDto aiChatLogWebDto) {
        AiChatLogBusinessDto aiChatLogBusinessDto = BeanUtil.copyProperties(aiChatLogWebDto, AiChatLogBusinessDto.class);
        Page<AiChatLogBusinessView> promptInfoPage = aiSmartBusiness.getChatLogList(aiChatLogBusinessDto);
        WebPageInfo<AiChatLogBusinessView> result = WebPageInfo.pageConversion(promptInfoPage, AiChatLogBusinessView.class);
        return WebResult.success(result);
    }


    @Login
    @RequestMapping(value = "/savePPTKey", method = RequestMethod.POST)
    public WebResult<String> savePPTKey(@RequestBody PPTKeyReq pptKeyReq) {
        if (!StringUtils.hasText(pptKeyReq.getLoginEmployeeId())) {
            return WebResult.error("500", "员工 ID 不存在");
        }
        // loginEmployeeId
        stringRedisTemplate.opsForValue().set(prefixPPt + pptKeyReq.getLoginEmployeeId(), JSONObject.toJSONString(pptKeyReq));
        stringRedisTemplate.opsForValue().set(prefixPPt + "keycount:" + pptKeyReq.getLoginEmployeeId(), "80");
        return WebResult.success("保存成功");
    }


    @RequestMapping(value = "/delPPTKey", method = RequestMethod.POST)
    public WebResult<String> delPPTKey(@RequestBody PPTKeyReq pptKeyReq) {
        if (!StringUtils.hasText(pptKeyReq.getLoginEmployeeId())) {
            return WebResult.error("500", "员工 ID 不存在");
        }
        // loginEmployeeId
        stringRedisTemplate.delete(prefixPPt + pptKeyReq.getLoginEmployeeId());
        stringRedisTemplate.delete(prefixPPt + "keycount:" + pptKeyReq.getLoginEmployeeId());
        return WebResult.success("删除成功");
    }

    @Login
    @RequestMapping(value = "/resetPPTKeyCount", method = RequestMethod.POST)
    public WebResult<String> resetPPTKeyCount(@RequestBody PPTKeyReq pptKeyReq) {
        if (!StringUtils.hasText(pptKeyReq.getLoginEmployeeId())) {
            return WebResult.error("500", "员工 ID 不存在");
        }
        stringRedisTemplate.opsForValue().set(prefixPPt + "keycount:" + pptKeyReq.getLoginEmployeeId(), pptKeyReq.getPPTKeyCount());
        return WebResult.success("保存成功");
    }


    @Login
    @RequestMapping(value = "/getPPTKeyUsed", method = RequestMethod.POST)
    public WebResult<JSONObject> getPPTKeyUsed(@RequestBody PPTKeyReq pptKeyReq) {
        if (!StringUtils.hasText(pptKeyReq.getLoginEmployeeId())) {
            return WebResult.error("500", "员工 ID 不存在");
        }
        JSONObject jsonObject = new JSONObject();
        // loginEmployeeId
        String value = stringRedisTemplate.opsForValue().get(prefixPPt + "keycount:" + pptKeyReq.getLoginEmployeeId());
        if (!StringUtils.hasText(value)) {
            jsonObject.put("remainCount", "-1");
        } else {
            jsonObject.put("remainCount", value);
        }
        return WebResult.success(jsonObject);
    }

    @RequestMapping(value = "/getCustomerInfoByName", method = RequestMethod.POST)
    public WebResult<CustomerInfoByNameWebView> getCustomerInfoByName(@RequestBody @Valid CustomerInfoByNameWebDto customerInfoByNameWebDto) {
        CustomerInfoByNameBusinessDto customerInfoByNameBusinessDto = BeanUtil.copyProperties(customerInfoByNameWebDto, CustomerInfoByNameBusinessDto.class);
        CustomerInfoByNameBusinessView result = aiService.getCustomerInfoByName(customerInfoByNameBusinessDto);
        return WebResult.success(BeanUtil.copyProperties(result, CustomerInfoByNameWebView.class));
    }

    @RequestMapping(value = "/completions/test", method = RequestMethod.POST)
    public WebResult<ExhibitionCompanyKjDubboView> test(@RequestBody AiQueryDto aiQueryDto) {
        ExhibitionCompanyKjDubboView exhibitionCompany = aiCustCacheBusiness.getExhibitionCompany(aiQueryDto.getPid(), aiQueryDto.getCustId());
        return WebResult.success(exhibitionCompany);
    }

    @RequestMapping(value = "/completions/test1", method = RequestMethod.POST)
    public WebResult<String> test1(@RequestBody AiChatLogWebDto aiChatLogWebDto) {
        List<SubVisitSigningView> subVisitSigningNewViews = aiService.buildSubVisitSigningDataNew(aiChatLogWebDto.getPid());
        String jsonString = JSONObject.toJSONString(subVisitSigningNewViews);
        return WebResult.success(jsonString);
    }


    @Login
    @RequestMapping(value = "/person/auth", method = RequestMethod.POST)
    public WebResult<JSONObject> personAuth(@RequestBody(required = false) LoginInfo aiQueryDto) {
        return WebResult.success(aiService.personAuth(aiQueryDto));
    }

    @Login
    @RequestMapping(value = "/person/addAuth", method = RequestMethod.POST)
    public WebResult<Boolean> addAuth(@RequestBody(required = false) AiQueryDto aiQueryDto) {
        aiService.saveAllAiAccessAuth(aiQueryDto.getAiAccessAuths(), aiQueryDto.getLoginEmployeeId());
        return WebResult.success(true);
    }

    @Login
    @RequestMapping(value = "/person/authList", method = RequestMethod.POST)
    public WebResult<JSONObject> authList(@RequestBody(required = false) LoginInfo aiQueryDto) {
        JSONObject jsonObjectRes = new JSONObject();
        List<String> authEmployeeIds = aiAccessAuthService.lambdaQuery().select(AiAccessAuth::getEmployeeId).list().stream().map(AiAccessAuth::getEmployeeId).distinct().collect(Collectors.toList());
        jsonObjectRes.put("authEmployeeIds", authEmployeeIds);
        return WebResult.success(jsonObjectRes);
    }


    @RequestMapping(value = "/errorAlert", method = RequestMethod.POST)
    public WebResult<?> errorAlert(@RequestBody Map<String, Object> bodyReq) {
        String url = bodyReq.getOrDefault("url", "").toString();
        String bodyReqStr = JSONObject.toJSONString(bodyReq.getOrDefault("body", ""));
        this.errorAlertWechat(url, bodyReqStr);
        try {
            String pid = bodyReq.getOrDefault("pid", "").toString();
            String custId = bodyReq.getOrDefault("custId", "").toString();
            String promptId = bodyReq.getOrDefault("promptId", "").toString();
            if (StringUtils.hasText(custId) && StringUtils.isEmpty(pid)) {
                Optional<CustomerDataThirdView> customerData = customerThirdService.getCustomerData(custId);
                if (customerData.isPresent()) {
                    CustomerDataThirdView customerDataThirdView = customerData.get();
                    pid = customerDataThirdView.getSourceDataId();
                }
            }
            if (StringUtils.hasText(pid) && StringUtils.hasText(promptId)) {
                AiCustCacheDubboView aiCustCacheDubboView = aiCustCacheHandler.get(pid + "_" + promptId);
                String key = UUID.randomUUID().toString().replace("-", "");
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("msgtype", "text");
                JSONObject jsonObjectBody = new JSONObject();
                if (Objects.nonNull(aiCustCacheDubboView)) {
                    redisOperator.set("CRM:AI:ERROR:CACHE:" + key, JSONObject.toJSONString(aiCustCacheDubboView), 1, TimeUnit.DAYS);
                    jsonObjectBody.put("content", "pid:" + pid + ",key:" + key + ",promptId:" + promptId);
                } else {
                    jsonObjectBody.put("content", "获取不到对应缓存");
                }
                jsonObject.put("text", jsonObjectBody);
                this.errorAlertWechat(url, JSONObject.toJSONString(jsonObject));
            }
        } catch (Exception e) {
            log.error("保存缓存异常,请求参数:" + JSONObject.toJSONString(bodyReq), e);
        }
        return WebResult.success();
    }

    @GetMapping(value = "/errorAlertGet")
    public WebResult<String> errorAlertGet(@RequestParam("key") String key) {
        return WebResult.success(redisOperator.get("CRM:AI:ERROR:CACHE:" + key));
    }

    private void errorAlertWechat(String url, String bodyReq) {
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(1, TimeUnit.MINUTES)
                    .readTimeout(1, TimeUnit.MINUTES)
                    .writeTimeout(1, TimeUnit.MINUTES)
                    .build();
            okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json;charset=UTF-8");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, bodyReq);
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("Accept", "application/json, text/plain, */*")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type", "application/json;charset=UTF-8")
                    .build();
            client.newCall(request).execute();
        } catch (Exception e) {
            log.error("alert异常通知异常", e);
        }
    }


    /**
     * 判断是否输入 master分组
     */
    private List<String> getCompanyEmployeeAiPromote(String employeeId) {
        List<String> strings = new ArrayList<>();
        try {
            JSONObject jsonObject = JSONObject.parseObject(nlpProperties.getPromotePerson());
            for (String key : jsonObject.keySet()) {
                JSONArray jsonArray = jsonObject.getJSONArray(key);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject json = jsonArray.getJSONObject(i);
                    String employeeIdJson = json.getString("employeeId");
                    if (employeeIdJson.equals(employeeId)) {
                        strings.add(key);
                    }
                }
            }
            if (strings.contains("master")) {
                strings = new ArrayList<>(jsonObject.keySet());
            }
            return strings;
        } catch (Exception e) {
            log.error("解析获取人员分组异常", e);
        }
        return new ArrayList<>();
    }

    private List<String> getAllEmployeeIds() {
        List<String> strings = new ArrayList<>();
        try {
            JSONObject jsonObject = JSONObject.parseObject(nlpProperties.getPromotePerson());
            for (String key : jsonObject.keySet()) {
                JSONArray jsonArray = jsonObject.getJSONArray(key);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject json = jsonArray.getJSONObject(i);
                    String employeeId = json.getString("employeeId");
                    strings.add(employeeId);
                }
            }
        } catch (Exception e) {
            return new ArrayList<>();
        }
        return strings.stream().distinct().collect(Collectors.toList());
    }

    public static boolean containsUrl(String text) {
        String regex = "\\bhttps?://[^\\s/$.?#].[\\S]*\\b";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        return matcher.find();
    }

    /***
     * 根据海关产品名称查询产品信息
     * @param customsProductWebDto
     * <AUTHOR>
     * @date 2025/5/21 11:49
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.util.List < com.ce.scrm.center.dao.entity.CustomsProduct>>
     **/
    //@Login
    @RequestMapping(value = "/customs/product/name", method = RequestMethod.POST)
    public WebResult<List<CustomsProduct>> customsProductListByName(@RequestBody(required = false) AiCustomsProductWebDto customsProductWebDto) {
        if (customsProductWebDto == null || StringUtils.isEmpty(customsProductWebDto.getProductName())) {
            return WebResult.success(Lists.newArrayList());
        }
        List<CustomsProduct> list = customsProductBusiness.getCustomsProductLikeName(customsProductWebDto.getProductName());
        return WebResult.success(list);
    }

    private List<Long> matchKeywords(String content, Map<Long, List<String>> keywordMap) {
        List<Long> ids = new ArrayList<>();
        if (StringUtils.hasText(content)) {
            content = content.toUpperCase();
            for (Map.Entry<Long, List<String>> entry : keywordMap.entrySet()) {
                Long id = entry.getKey();
                List<String> keywords = new ArrayList<>(entry.getValue());
                for (String keyword : keywords) {
                    if (content.contains(keyword.toUpperCase())) {
                        ids.add(id);
                    }
                }
            }
        }
        return ids;
    }


    @Login
    @RequestMapping(value = "/getResPageList", produces = {"application/json;charset=UTF-8"})
    public WebResult<?> getResPageList(@RequestBody VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        log.info("当前登录人信息:{}", JSONObject.toJSONString(voiceAnalyzeWebDto));
        Page<AiVoiceAnalyze> page = aiService.getVoiceInfoPageList(voiceAnalyzeWebDto);
        WebPageInfo result = WebPageInfo.pageConversion(page, AiVoiceAnalyze.class);
        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(result.getList()));
        List<JSONObject> jsonObjectList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            try {
                jsonObject.put("createTime", simpleDateFormat.format(jsonObject.getDate("createTime")));
            } catch (Exception e) {
                jsonObject.put("createTime", "");
            }
            try {
                jsonObject.put("updateTime", simpleDateFormat.format(jsonObject.getDate("updateTime")));
            } catch (Exception e) {
                jsonObject.put("updateTime", "");
            }
            jsonObjectList.add(jsonObject);
        }
        result.setList(jsonObjectList);
        return WebResult.success(result);
    }

    /***
     * 录音重新分析
     * @param voiceAnalyzeWebDto
     * <AUTHOR>
     * @date 2025/7/11 21:35
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<?>
     **/
    @Login
    @RequestMapping(value = "/voiceReAnalyze", produces = {"application/json;charset=UTF-8"})
    public WebResult<?> voiceReAnalyze(@RequestBody VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        log.info("当前登录人信息:{}", JSONObject.toJSONString(voiceAnalyzeWebDto));
        JSONObject jsonObject = aiService.voiceReAnalyze(voiceAnalyzeWebDto);
        if(null == jsonObject){
            return WebResult.error("400","录音分析异常");
        }
        return WebResult.success(jsonObject);
    }


    @Login
    @RequestMapping(value = "/getVoiceInfoById", produces = {"application/json;charset=UTF-8"})
    public WebResult<?> getVoiceInfoById(@RequestBody VoiceAnalyzeWebDto voiceAnalyzeWebDto) {
        log.info("当前登录人信息:{}", JSONObject.toJSONString(voiceAnalyzeWebDto));
        AiVoiceAnalyze analyze = aiService.getVoiceInfoById(voiceAnalyzeWebDto);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(analyze));
        jsonObject.put("businessChatResponse", JSONObject.parseObject(jsonObject.getString("businessChatResponse")));
        return WebResult.success(jsonObject);
    }

    /***
     * 录音分析提交
     * @param voiceAnalyzeWebDto
     * <AUTHOR>
     * @date 2025/7/11 21:35
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<?>
     **/
    @Login
    @RequestMapping(value = "/voiceAnalyze", produces = {"application/json;charset=UTF-8"})
    public WebResult<?> voiceAnalyze(@RequestBody VoiceUpload voiceAnalyzeWebDto) {
        log.info("当前登录人信息:{}", JSONObject.toJSONString(voiceAnalyzeWebDto));
        JSONObject jsonObject = aiService.uploadFiles(voiceAnalyzeWebDto);
        if (null == jsonObject) {
            return WebResult.error("400", "录音分析异常");
        }
        return WebResult.success(jsonObject);
    }

}
