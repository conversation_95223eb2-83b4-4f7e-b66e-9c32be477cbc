package com.ce.scrm.center.web.controller.ai;

import cn.ce.cesupport.enums.YesOrNoEnum;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.AiEventLog;
import com.ce.scrm.center.dao.entity.AiPromptInternalInfo;
import com.ce.scrm.center.dao.service.AiEventLogService;
import com.ce.scrm.center.dao.service.AiPromptInternalInfoService;
import com.ce.scrm.center.web.entity.dto.AiQueryDto;
import com.ce.scrm.center.web.entity.dto.ContractAnalysisRequest;
import com.ce.scrm.center.web.entity.dto.ContractQuerySmartDto;
import com.ce.scrm.center.web.entity.dto.ai.AiOrgPerformanceInfo;
import com.ce.scrm.center.web.entity.dto.ai.QueryAiPromptInfoWebDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.gptstreamutil.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
@RestController
@RequestMapping("analysis/internal")
public class AiSmartInternalController {

    @Autowired
    private AiService aiService;

    @Autowired
    private AiPromptInternalInfoService aiPromptInternalInfoService;

    @Autowired
    private HaiGuanService haiGuanService;

    @Autowired
    private AiEventLogService aiEventLogService;

    @RequestMapping(value = "/completions/getAnalysisiTips", method = RequestMethod.POST, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> getAiTips(@RequestBody AiQueryDto aiQueryDto) {
        try {
            String systemContent = null;
            if (StringUtils.isNotBlank(aiQueryDto.getSystemContent())) {
                systemContent = aiQueryDto.getSystemContent();
            } else {
                AiPromptInternalInfo aiPromptInternalInfo = aiPromptInternalInfoService.getById(aiQueryDto.getPromptId());
                systemContent = aiPromptInternalInfo.getContent();
            }
            String analyzerJson = StringUtils.isBlank(aiQueryDto.getAnalyzeJson()) ? aiService.buildPerformanceAndVisitSigningData(aiQueryDto.getOrgId()) : aiQueryDto.getAnalyzeJson();
            SseEmitter sseEmitter = SseEmitterUtils.getSseEmitter();
            return aiService.getAiTips(analyzerJson, messageRes -> {
            }, ExtralInfo.builder()
                    .promptId(aiQueryDto.getPromptId())
                    .promptContent(systemContent)
                    .build(), sseEmitter);
        } catch (Exception e) {
            log.error("请求失败", e);
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, errorSseEmitter);
            return ResponseEntity.ok(errorSseEmitter);
        }
    }

    @RequestMapping(value = "/getAnalysisPromptInfoPage", method = RequestMethod.POST)
    public WebResult<WebPageInfo<AiPromptInternalInfo>> getAnalysisPromptInfoPage(@RequestBody QueryAiPromptInfoWebDto queryAiPromptInfoWebDto) {
        Page<AiPromptInternalInfo> result = aiPromptInternalInfoService
                .page(new Page<>(queryAiPromptInfoWebDto.getPageNum(), queryAiPromptInfoWebDto.getPageSize()),
                        new LambdaQueryWrapper<AiPromptInternalInfo>().eq(AiPromptInternalInfo::getDeleteFlag, YesOrNoEnum.NO.getCode()));
        return WebResult.success(WebPageInfo.pageConversion(result, AiPromptInternalInfo.class));
    }

    @RequestMapping(value = "/saveOrUpdateAnalysisPrompt", method = RequestMethod.POST)
    public WebResult<Boolean> saveOrUpdateAnalysisPrompt(@RequestBody @Valid AiPromptInternalInfo aiPromptInternalInfo) {
        boolean saveOrUpdate = aiPromptInternalInfoService.saveOrUpdate(aiPromptInternalInfo);
        return WebResult.success(saveOrUpdate);
    }

    @PostMapping("getOrgPerformanaceData")
    public WebResult<JSONObject> getOrgPerformanaceData(@RequestBody @Valid AiOrgPerformanceInfo aiPerformanaceData) {
        String performanceData = aiService.buildPerformanceAndVisitSigningData(aiPerformanaceData.getOrgId());
        return WebResult.success(JSONObject.parseObject(performanceData));
    }


    @RequestMapping(value = "gethaiguan", method = RequestMethod.GET)
    public String getDemoHtml() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>API请求工具</title>\n" +
                "    <script src=\"https://cdn.tailwindcss.com\"></script>\n" +
                "    <link href=\"https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css\" rel=\"stylesheet\">\n" +
                "    \n" +
                "    <script>\n" +
                "        tailwind.config = {\n" +
                "            theme: {\n" +
                "                extend: {\n" +
                "                    colors: {\n" +
                "                        primary: '#4F46E5',\n" +
                "                        secondary: '#10B981',\n" +
                "                        neutral: '#1F2937',\n" +
                "                    },\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    </script>\n" +
                "    \n" +
                "    <style type=\"text/tailwindcss\">\n" +
                "        @layer utilities {\n" +
                "            .transition-all-300 {\n" +
                "                transition: all 0.3s ease;\n" +
                "            }\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body class=\"bg-gray-50 min-h-screen p-4 md:p-8\">\n" +
                "    <div class=\"max-w-3xl mx-auto\">\n" +
                "        <!-- 页面标题 -->\n" +
                "        <div class=\"text-center mb-8\">\n" +
                "            <h1 class=\"text-2xl md:text-3xl font-bold text-neutral mb-2\">查询海关出口数据语言信息</h1>\n" +
                "            <p class=\"text-gray-500\">发送请求并查看响应结果</p>\n" +
                "        </div>\n" +
                "        \n" +
                "        <!-- 输入区域 -->\n" +
                "        <div class=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n" +
                "            <div class=\"mb-4\">\n" +
                "                <label for=\"content\" class=\"block text-sm font-medium text-gray-700 mb-1\">请求内容</label>\n" +
                "                <textarea \n" +
                "                    id=\"content\" \n" +
                "                    rows=\"3\" \n" +
                "                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all-300\"\n" +
                "                    placeholder=\"请输入请求内容...\"\n" +
                "                ></textarea>\n" +
                "                <p class=\"text-xs text-gray-500 mt-1\">查询国家排序 orderby desc 年 ,总交易量  limit 5 </p>\n" +
                "            </div>\n" +
                "            \n" +
                "            <div class=\"flex justify-end\">\n" +
                "                <button \n" +
                "                    id=\"submitBtn\" \n" +
                "                    class=\"bg-primary hover:bg-primary/90 text-white px-5 py-2 rounded-md transition-all-300 flex items-center gap-2\"\n" +
                "                >\n" +
                "                    <i class=\"fa fa-paper-plane\"></i>\n" +
                "                    <span>发送请求</span>\n" +
                "                </button>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "        \n" +
                "        <!-- 加载状态 -->\n" +
                "        <div id=\"loading\" class=\"hidden flex justify-center py-6\">\n" +
                "            <div class=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary\"></div>\n" +
                "        </div>\n" +
                "        \n" +
                "        <!-- 响应结果区域 -->\n" +
                "        <div id=\"responseArea\" class=\"hidden bg-white rounded-lg shadow-md p-6\">\n" +
                "            <h2 class=\"text-lg font-semibold text-neutral mb-3 flex items-center\">\n" +
                "                <i class=\"fa fa-server text-primary mr-2\"></i>响应结果\n" +
                "            </h2>\n" +
                "            <div class=\"bg-gray-50 p-4 rounded-md overflow-auto max-h-96\">\n" +
                "                <pre id=\"responseContent\" class=\"text-sm text-gray-700 whitespace-pre-wrap\"></pre>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "        \n" +
                "        <!-- 错误提示区域 -->\n" +
                "        <div id=\"errorArea\" class=\"hidden bg-red-50 border border-red-100 rounded-lg p-6\">\n" +
                "            <h2 class=\"text-lg font-semibold text-red-700 mb-3 flex items-center\">\n" +
                "                <i class=\"fa fa-exclamation-circle mr-2\"></i>请求错误\n" +
                "            </h2>\n" +
                "            <p id=\"errorMessage\" class=\"text-red-600\"></p>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "\n" +
                "    <script>\n" +
                "        // 获取DOM元素\n" +
                "        const contentInput = document.getElementById('content');\n" +
                "        const submitBtn = document.getElementById('submitBtn');\n" +
                "        const loading = document.getElementById('loading');\n" +
                "        const responseArea = document.getElementById('responseArea');\n" +
                "        const responseContent = document.getElementById('responseContent');\n" +
                "        const errorArea = document.getElementById('errorArea');\n" +
                "        const errorMessage = document.getElementById('errorMessage');\n" +
                "        \n" +
                "        // 提交按钮点击事件\n" +
                "        submitBtn.addEventListener('click', async () => {\n" +
                "            const content = contentInput.value.trim();\n" +
                "            \n" +
                "            // 验证输入\n" +
                "            if (!content) {\n" +
                "                showError('请输入请求内容');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            // 准备发送请求\n" +
                "            showLoading();\n" +
                "            hideResponse();\n" +
                "            hideError();\n" +
                "            \n" +
                "            try {\n" +
                "                // 构建请求数据\n" +
                "                const requestData = {\n" +
                "                    content: content\n" +
                "                };\n" +
                "                \n" +
                "                // 发送请求\n" +
                "                const response = await fetch(\n" +
                "                    'getDemoHtmls',\n" +
                "                    {\n" +
                "                        method: 'POST',\n" +
                "                        headers: {\n" +
                "                            'Authorization': 'Bearer sk-7de039270e844634be26fb38451b4867',\n" +
                "                            'Content-Type': 'application/json',\n" +
                "                        },\n" +
                "                        body: JSON.stringify(requestData)\n" +
                "                    }\n" +
                "                );\n" +
                "                \n" +
                "                // 处理响应\n" +
                "                if (!response.ok) {\n" +
                "                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);\n" +
                "                }\n" +
                "                \n" +
                "                // 尝试解析响应内容\n" +
                "                let responseData;\n" +
                "                const contentType = response.headers.get('content-type');\n" +
                "                if (contentType && contentType.includes('application/json')) {\n" +
                "                    responseData = await response.json();\n" +
                "                    // 格式化JSON以便显示\n" +
                "                    responseContent.textContent = JSON.stringify(responseData, null, 2);\n" +
                "                } else {\n" +
                "                    responseData = await response.text();\n" +
                "                    responseContent.textContent = responseData;\n" +
                "                }\n" +
                "                \n" +
                "                showResponse();\n" +
                "            } catch (error) {\n" +
                "                showError(error.message);\n" +
                "            } finally {\n" +
                "                hideLoading();\n" +
                "            }\n" +
                "        });\n" +
                "        \n" +
                "        // 显示加载状态\n" +
                "        function showLoading() {\n" +
                "            loading.classList.remove('hidden');\n" +
                "            submitBtn.disabled = true;\n" +
                "            submitBtn.innerHTML = '<i class=\"fa fa-spinner fa-spin\"></i><span>发送中...</span>';\n" +
                "        }\n" +
                "        \n" +
                "        // 隐藏加载状态\n" +
                "        function hideLoading() {\n" +
                "            loading.classList.add('hidden');\n" +
                "            submitBtn.disabled = false;\n" +
                "            submitBtn.innerHTML = '<i class=\"fa fa-paper-plane\"></i><span>发送请求</span>';\n" +
                "        }\n" +
                "        \n" +
                "        // 显示响应结果\n" +
                "        function showResponse() {\n" +
                "            responseArea.classList.remove('hidden');\n" +
                "        }\n" +
                "        \n" +
                "        // 隐藏响应结果\n" +
                "        function hideResponse() {\n" +
                "            responseArea.classList.add('hidden');\n" +
                "        }\n" +
                "        \n" +
                "        // 显示错误信息\n" +
                "        function showError(message) {\n" +
                "            errorMessage.textContent = message;\n" +
                "            errorArea.classList.remove('hidden');\n" +
                "        }\n" +
                "        \n" +
                "        // 隐藏错误信息\n" +
                "        function hideError() {\n" +
                "            errorArea.classList.add('hidden');\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>\n" +
                "    ";
    }




    @RequestMapping(value = "getDemoHtmls", method = RequestMethod.POST)
    public String getDemoHtmls(@RequestBody Map<String, Object> content) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            List<JSONObject> resultData = haiGuanService.getHaiGunInfo(content.get("content").toString());
            stringBuilder.append("海关库top5结果\n").append(JSONObject.toJSONString(resultData, SerializerFeature.PrettyFormat));
        } catch (Exception e) {
            log.error("请求失败", e);
            return "请求失败:" + stringBuilder;
        }
        return stringBuilder.toString();
    }

    /***
     * 合同分析
     * @param contractAnalysisRequest
     * <AUTHOR>
     * @date 2025/8/13 15:16
     * @version 1.0.0
     * @return org.springframework.http.ResponseEntity<org.springframework.web.servlet.mvc.method.annotation.SseEmitter>
    **/
    @RequestMapping(value = "/contract/getAnalysisiTips", method = RequestMethod.POST, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> getContractAiTips(@RequestBody ContractAnalysisRequest contractAnalysisRequest) {
        try {
            String chatId = contractAnalysisRequest.getChatId();
            log.info("合同分析开始,chatId={}", chatId);
            String analyzerJson = parseToStringFromUrl(contractAnalysisRequest.getFileUrl());
            String systemContent = contractAnalysisRequest.getSystemContent();
            //萃取
            if (Objects.equals(11,contractAnalysisRequest.getQuestionType())){
                systemContent= formatExtractPrompt(systemContent);
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("合同内容萃取开始").append("\n").
                        append("合同地址:").append(contractAnalysisRequest.getFileUrl());
                sendWechatMessage(stringBuffer.toString());
            }
            //合同风险
            if (Objects.equals(12,contractAnalysisRequest.getQuestionType())){
                systemContent= formatRiskPrompt(systemContent);
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("合同风险评估开始").append("\n").
                        append("合同地址:").append(contractAnalysisRequest.getFileUrl());
                sendWechatMessage(stringBuffer.toString());
            }
            if (StringUtils.isBlank(analyzerJson)){
                SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
                SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, errorSseEmitter);
                return ResponseEntity.ok(errorSseEmitter);
            }
            SseEmitter sseEmitter = SseEmitterUtils.getSseEmitter();
            return aiService.getAiTips(analyzerJson, messageRes -> {
                AiEventLog aiEventLog = new AiEventLog();
                aiEventLog.setEventId(contractAnalysisRequest.getChatId());
                aiEventLog.setParentEventId(contractAnalysisRequest.getChatId());
                aiEventLog.setEmployeeId(contractAnalysisRequest.getLoginEmployeeId());
                aiEventLog.setEmployeeName(contractAnalysisRequest.getLoginEmployeeName());
                aiEventLog.setPlatform(contractAnalysisRequest.getPlatform());
                aiEventLog.setChatReasoning(messageRes.getReasoningContent());
                aiEventLog.setChatResponse(messageRes.getContent());
                aiEventLog.setQuestionType(contractAnalysisRequest.getQuestionType());
                aiEventLog.setOperateType(6);
                aiEventLogService.save(aiEventLog);
            }, ExtralInfo.builder()
                    .promptId(contractAnalysisRequest.getPromptId())
                    .channelId(contractAnalysisRequest.getChannelId())
                    .chatId(chatId)
                    .eventId(chatId)
                    .promptContent(systemContent)
                    .build(), sseEmitter);
        } catch (Exception e) {
            log.error("请求失败", e);
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, errorSseEmitter);
            return ResponseEntity.ok(errorSseEmitter);
        }
    }

    /***
     * tika 获取文档内容
     * @param path
     * <AUTHOR>
     * @date 2025/8/13 15:21
     * @version 1.0.0
     * @return java.lang.String
    **/
    private  String parseToStringFromUrl(String path){
        try {
            Tika tika = new Tika();
            URL url = new URL(path);
            return tika.parseToString(url.openStream());
        } catch (Exception e) {
            log.error("获取文档内容失败,path={}",path);
        }
        return null;
    }

    /***
     * 风险
     * @param content
     * <AUTHOR>
     * @date 2025/8/13 23:34
     * @version 1.0.0
     * @return java.lang.String
    **/
    private String formatRiskPrompt(String content){
        String integration = "中企动力和中期跨境可以为客户提供以下18种核心服务\n" +
                "**核心业务范围：**\n" +
                "- 网站设计与开发\n" +
                "- 前端界面设计\n" +
                "- 后端系统开发\n" +
                "- 域名注册与解析\n" +
                "- 网站托管与维护\n" +
                "- SEO优化服务\n" +
                "- 移动端网站/小程序开发\n" +
                "- 电商网站建设\n" +
                "- 企业官网建设\n" +
                "- CMS内容管理系统\n" +
                "- 广告投放\n" +
                "- 社媒待运营\n" +
                "- UI/UX设计\n" +
                "- 网站安全防护\n" +
                "- 数据备份服务\n" +
                "- 技术培训与支持\n" +
                "- 网站性能优化\n" +
                "- 软件开发（非网站类）\n" +
                "\n" +
                "你是“中企动力”和“中企跨境”这两家公司的资深合同风险审查专家。\n" +
                "你必须遵循“穿透式分析”原则，既要识别显性风险条款，也要挖掘隐性风险（如条款冲突、表述模糊、逻辑漏洞等），并结合《民法典》，《技术合同司法解释》，《计算机软件保护条例》，《网络安全审查办法》，《网络安全法》，《数据安全法》，及相关行业法规，评估风险可能导致的法律后果（如合同无效、履行障碍、赔偿责任等）。具体审查框架及重点如下：\n" +
                "\n" +
                "%s\n" +
                "\n" +
                "你必须严格基于合同文本及法律规定进行分析，不加入主观推测，确保风险点全面、准确、可追溯。\n" +
                "你必须在乙方立场进行合同审核，确保乙方利益最大化。\n" +
                "你必须根据以上16项维度输出“合同风险评估表（己方利益最大化）”，表中必须包含“风险类型，对应条款，风险原文，风险分析，法律依据，可能后果，建议”，并且通过表格形式输出";
        return String.format(integration, content);
    }


    /**
     * 萃取
     * @param content
     * @return
     */
    private String formatExtractPrompt(String content) {
        String integration = "你作为专业的法律顾问，协助我从以下合同文本中提取关键且重要的信息。为确保信息全面、无遗漏，方便快速掌握合同核心内容，请按照以下框架进行提取，如合同中存在相关内容，务必详细列明；如合同中无对应内容，也请注明“无相关约定”。\n" +
                "必须以 “合同核心信息全量提取” 为目标；\n" +
                "必须对提取过程需遵循 “原文优先、穷尽挖掘、精准映射” 原则，所有提取信息均源自合同原文，不许做主观推断；\n" +
                "必须覆盖合同全部实质性条款，即使是补充协议、附件中的内容也需整合；\n" +
                "对模糊表述或特殊约定，需原文摘录并标注位置（如 “见第 X 条第 X 款”）。\n" +
                "\n" +
                "%s\n" +
                "\n" +
                "注意：不许做主观推断\n" +
                "必须严格按照上述框架提取信息，确保每个条款的内容均来自合同原文，不添加主观解读。如合同中存在模糊表述或冲突条款，需在对应部分注明具体原文内容及冲突点，以便进一步核实。";
        return String.format(integration, content);
    }
    /**
     * 合同聊天接口
     */
    @RequestMapping(value = "/contract/getChatCompletions", method = RequestMethod.POST, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> getContractChatComplete(@RequestBody ContractAnalysisRequest contractAnalysisRequest) {
        log.info("请求信息getContractChatComplete:{}", JSONObject.toJSONString(contractAnalysisRequest));
        String content  =parseToStringFromUrl(contractAnalysisRequest.getFileUrl());
        if (StringUtils.isBlank(content)){
            SseEmitter errorSseEmitter = SseEmitterUtils.getSseEmitter();
            SseEmitterUtils.sendMessage(ResponseConstant.ERROR_RESPONSE_MESSAGE, errorSseEmitter);
            return ResponseEntity.ok(errorSseEmitter);
        }

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("合同聊天开始:").append("\n").
                append("合同地址:").append(contractAnalysisRequest.getFileUrl()).append("\n")
                .append("用户问题:").append(contractAnalysisRequest.getChatQuestion());
        sendWechatMessage(stringBuffer.toString());
        ContractQuerySmartDto aiQueryDto = new ContractQuerySmartDto();
        aiQueryDto.setChannelId(contractAnalysisRequest.getChannelId());
        aiQueryDto.setPlatform(contractAnalysisRequest.getPlatform());
        aiQueryDto.setChatId(contractAnalysisRequest.getChatId());
        aiQueryDto.setNeedSearch(false);
        aiQueryDto.setImageGenerator(false);
        aiQueryDto.setQuestionSource(0);
        aiQueryDto.setQuestionType(contractAnalysisRequest.getQuestionType());
        aiQueryDto.setOperateType(contractAnalysisRequest.getOperateType());
        aiQueryDto.setParentEventId(contractAnalysisRequest.getParentEventId());
        aiQueryDto.setChatQuestion(contractAnalysisRequest.getChatQuestion());
        aiQueryDto.setContent(content);
        return aiService.getContractChatComplete(aiQueryDto);
    }

    /**
     * 消息最大长度
     */
    private final static int MSG_MAX_LENGTH = 4096;

    public Boolean sendWechatMessage(String message) {
        try {
            if (message.length() > MSG_MAX_LENGTH) {
                message = message.substring(0, MSG_MAX_LENGTH);
            }
            Map<String, String> contentMap = new HashMap<>();
            contentMap.put("content", message);
            Map<String, Object> param = new HashMap<>();
            param.put("msgtype", "markdown");
            param.put("markdown", contentMap);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");
            okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, JSONObject.toJSONString(param));
            Request request = new Request.Builder()
                    .url("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0e80a937-7630-4eb2-a687-1db6a4e8f00b")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            response.close();
        } catch (Exception e) {
            log.error("发送合同消息失败",e);
        }
        return true;
    }
}
