package com.ce.scrm.center.web.controller.ai;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.AiEventLog;
import com.ce.scrm.center.dao.entity.AiPromptInfo;
import com.ce.scrm.center.dao.mapper.AiEventLogMapper;
import com.ce.scrm.center.service.business.entity.ai.AiEventLogDto;
import com.ce.scrm.center.service.business.entity.view.ai.AiPromptInfoBusinessView;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.entity.dto.AiQuerySmartDto;
import com.ce.scrm.center.web.entity.dto.ai.AiEventLogWebDto;
import com.ce.scrm.center.web.entity.response.WebPageInfo;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.gptstreamutil.AiService;
import com.ce.scrm.center.web.util.gptstreamutil.AiWebSiteDescGetService;
import com.ce.scrm.center.web.util.gptstreamutil.SseEmitterUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/2/24 17:28
 */
@Slf4j
@RestController
@RequestMapping("/analysis/smart")
public class AiSmartNewController {

    @Autowired
    private AiService aiService;
    @Autowired
    private AiEventLogMapper aiEventLogMapper;

    /**
     * 首次分析
     */
    @Deprecated
    @Login
    @RequestMapping(value = "/completions/getAnalysisiTips", method = RequestMethod.POST, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> getAnalysisiTips(@RequestBody AiQuerySmartDto aiQueryDto) {
        SseEmitter sseEmitter = SseEmitterUtils.getSseEmitter();
        return aiService.getAnalysisiTips(aiQueryDto,sseEmitter);
    }

    /**
     * 聊天接口
     */
    @Login
    @RequestMapping(value = "/completions/getChatCompletions", method = RequestMethod.POST, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> getAiChatComplete(@RequestBody AiQuerySmartDto aiQueryDto) {
        log.info("请求信息getAiChatComplete:{}", JSONObject.toJSONString(aiQueryDto));
        return aiService.getAiChatComplete(aiQueryDto);
    }

    /**
     * 点赞 low 故障上报  复制 等事件
     */
    @Login
    @RequestMapping(value = "/saveAnalysisStarLog", method = RequestMethod.POST)
    public WebResult<Boolean> saveAiStarLog(@RequestBody AiQuerySmartDto aiQuerySmartDto) {
        return WebResult.success(aiService.saveAiStarLog(aiQuerySmartDto));
    }


    /**
     * 前端主动点击生成 ppt
     */
    @Login
    @RequestMapping(value = "/generatorPPTFromAnalysisiOneTips", method = RequestMethod.POST)
    public WebResult<String> generatorPPTFromAnalysisiOneTips(@RequestBody AiQuerySmartDto aiQueryDto) {
        return aiService.generatorPPTFromAnalysisiOneTips(aiQueryDto);
    }



    @Login
    @RequestMapping(value = "/customs/retainCust", method = RequestMethod.POST)
    public WebResult<JSONObject> retainCust(@RequestBody AiQuerySmartDto aiQuerySmartDto) {
        Integer result = aiService.retainCust(aiQuerySmartDto.getCustId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("retainCust", result);
        return WebResult.success(jsonObject);
    }


    @Login
    @RequestMapping(value = "/getAnalysisPromptIdAndTitleListUsed", method = RequestMethod.POST)
    public WebResult<List<AiPromptInfoBusinessView>> getAnalysisPromptIdAndTitleListUsed(@RequestBody AiQuerySmartDto aiQuerySmartDto) {
        List<AiPromptInfo> result = aiService.getAllPromptsAnyUsed(aiQuerySmartDto);
        List<AiPromptInfoBusinessView> promptInfoPage = result.stream().map(T -> {
            AiPromptInfoBusinessView aiPromptInfoBusinessView = new AiPromptInfoBusinessView();
            BeanUtils.copyProperties(T, aiPromptInfoBusinessView);
            return aiPromptInfoBusinessView;
        }).collect(Collectors.toList());
        return WebResult.success(promptInfoPage);
    }

    @Login
    @RequestMapping(value = "/getImageGeneratorList", method = RequestMethod.POST)
    public WebResult<WebPageInfo<AiEventLog>> getImageGeneratorList(@RequestBody AiEventLogWebDto aiQueryDto) {
        log.info("当前登录信息:{}", JSONObject.toJSONString(aiQueryDto));
        AiEventLogDto aiEventLogWebDto = BeanUtil.copyProperties(aiQueryDto, AiEventLogDto.class);
        Page<AiEventLog> aiEventLogPage = aiService.getPageEventLog(aiEventLogWebDto);
        WebPageInfo<AiEventLog> result = WebPageInfo.pageConversion(aiEventLogPage, AiEventLog.class);
        return WebResult.success(result);
    }

    @Login
    @RequestMapping(value = "/getChatLog", method = RequestMethod.POST)
    public WebResult<WebPageInfo<AiEventLog>> getChatLog(@RequestBody AiQuerySmartDto aiStarInfoWebDto) {
        Page<AiEventLog> promptInfoPage = aiService.getPageEventLogChat(aiStarInfoWebDto);
        WebPageInfo<AiEventLog> result = WebPageInfo.pageConversion(promptInfoPage, AiEventLog.class);
        return WebResult.success(result);
    }

    @RequestMapping(value = "/website", method = RequestMethod.GET)
    public String getWebsiteHtml(@RequestParam("eventId") String eventId) {
        AiEventLog aiEventLog = aiEventLogMapper.selectOne(new LambdaQueryWrapper<AiEventLog>().eq(AiEventLog::getEventId, eventId));
        if (null == aiEventLog || !Objects.equals(aiEventLog.getQuestionType(), 10)) {
            return "<h1>404</h1>";
        } else {
            return aiEventLog.getChatResponse();
        }
    }

    @Autowired
    private AiWebSiteDescGetService aiWebSiteDescGetService;

    @RequestMapping(value = "getImageWebSitePage", method = RequestMethod.GET)
    public String getImageWebSitePage() {
        return aiWebSiteDescGetService.task("https://300.cn");
    }

}
