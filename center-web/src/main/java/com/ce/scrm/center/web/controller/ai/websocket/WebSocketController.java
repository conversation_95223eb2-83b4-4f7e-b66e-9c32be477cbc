package com.ce.scrm.center.web.controller.ai.websocket;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/ws")
public class WebSocketController {

    @Autowired
    private WebSocketHandler webSocketHandler;

    @PostMapping("/send")
    public String sendMessage(@RequestParam String sessionId, @RequestParam String msg) {
        try {
            webSocketHandler.sendMessageTo(sessionId, msg);
            return "消息发送成功";
        } catch (Exception e) {
            e.printStackTrace();
            return "发送失败: " + e.getMessage();
        }
    }

    @PostMapping("/broadcastMessage")
    public String sendMessage(@RequestBody String msg) {
        webSocketHandler.broadcastMessage(msg);
        return "广播消息成功";
    }

    @PostMapping("/*")
    public String aaa(@RequestParam Map<String,Object> map) {
        System.out.println("123123123123");
        return "123123123123";
    }
}
