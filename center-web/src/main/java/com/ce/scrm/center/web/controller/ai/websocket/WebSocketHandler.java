package com.ce.scrm.center.web.controller.ai.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.concurrent.ConcurrentHashMap;

@Component
public class WebSocketHandler extends TextWebSocketHandler {

    Logger log = LoggerFactory.getLogger(WebSocketHandler.class);

    //保存所有连接的 session（可以用 userId 作为 key 更合适）
    private static final ConcurrentHashMap<String, WebSocketSession> sessionMap = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("连接建立：" + session.getId());
        sessionMap.put(session.getId(), session); // 可自定义 key
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        log.info("收到消息：" + message.getPayload());
        session.sendMessage(new TextMessage(" {\"type\": \"pong\"}"));
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        log.info("连接关闭：" + session.getId());
        sessionMap.remove(session.getId());
    }

    public void sendMessageTo(String sessionId, String msg) throws Exception {
        log.info("发送消息：" + msg);
        WebSocketSession session = sessionMap.get(sessionId);
        if (session != null && session.isOpen()) {
            session.sendMessage(new TextMessage(msg));
        }
    }

    public ConcurrentHashMap<String, WebSocketSession> getSessionMap() {
        return sessionMap;
    }

    public void broadcastMessage(String msg) {
        sessionMap.values().forEach(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(msg));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
}
