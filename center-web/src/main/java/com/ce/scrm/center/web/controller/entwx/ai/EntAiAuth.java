package com.ce.scrm.center.web.controller.entwx.ai;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dao.entity.CustomsProduct;
import com.ce.scrm.center.service.business.CustomsProductBusiness;
import com.ce.scrm.center.web.aop.anno.Login;
import com.ce.scrm.center.web.aop.base.LoginInfo;
import com.ce.scrm.center.web.aop.base.LoginType;
import com.ce.scrm.center.web.entity.dto.ai.AiCustomsProductWebDto;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.gptstreamutil.AiService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/entwx/analysis")
@Login(loginType = LoginType.ENT_WECHAT)
public class EntAiAuth {

    @Autowired
    private AiService aiService;

    @Autowired
    CustomsProductBusiness customsProductBusiness;

    @PostMapping(value = "/person/auth")
    public WebResult<JSONObject> personAuth(@RequestBody LoginInfo loginInfo) {
        return WebResult.success(aiService.personAuth(loginInfo));
    }


    @RequestMapping(value = "/customs/product/name", method = RequestMethod.POST)
    public WebResult<List<CustomsProduct>> customsProductListByName(@RequestBody(required = false) AiCustomsProductWebDto customsProductWebDto) {
        if (customsProductWebDto == null || StringUtils.isEmpty(customsProductWebDto.getProductName())) {
            return WebResult.success(Lists.newArrayList());
        }
        List<CustomsProduct> list = customsProductBusiness.getCustomsProductLikeName(customsProductWebDto.getProductName());
        return WebResult.success(list);
    }

}
