package com.ce.scrm.center.web.controller.kj;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.center.web.entity.dto.marketingsms.SmsMessageDataDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.SendSmsHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description 跨境市场营销短信
 * <AUTHOR>
 * @Date 2025-06-30 16:37
 */
@Slf4j
@RestController
@RequestMapping("marketing")
public class MarketingSMSController {

	@PostMapping("/sendSms")
	public WebResult<?> sendSms(@Valid @RequestBody SmsMessageDataDto dto) {
		log.info("跨境市场营销短信sendSms={}", JSON.toJSONString(dto));
		switch (dto.getType()) {
			case 1:
				return WebResult.convertKjResultWebResult(SendSmsHttpUtils.sendMessage(dto));
			case 2:
				return WebResult.convertKjResultWebResult(SendSmsHttpUtils.sendDiffMessage(dto));
			case 3:
				return WebResult.convertKjResultWebResult(SendSmsHttpUtils.getReport(dto.getNumber()));
			case 4:
				return WebResult.convertKjResultWebResult(SendSmsHttpUtils.getMo(dto.getNumber()));
			case 5:
				return WebResult.convertKjResultWebResult(SendSmsHttpUtils.getSmsBalance());
			default:
				log.warn("不支持的短信类型={}", dto.getType());
				break;
		}
		return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
	}


}
