package com.ce.scrm.center.web.controller.openapi;

import cn.ce.cecloud.business.service.BusinessAppService;
import cn.ce.cesupport.base.utils.PositionUtil;
import cn.ce.cesupport.base.vo.ScrmResult;
import cn.ce.cesupport.enums.CodeMessageEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.dao.entity.CmCustProtect;
import com.ce.scrm.center.dao.service.CmCustProtectService;
import com.ce.scrm.center.service.business.CirculationLossBusiness;
import com.ce.scrm.center.service.business.EmployeeInfoBusiness;
import com.ce.scrm.center.service.business.entity.dto.CirculationLossPageBusinessDto;
import com.ce.scrm.center.service.business.entity.dto.EmployeeInfoBusinessDto;
import com.ce.scrm.center.service.business.entity.view.CirculationLossPageBusinessView;
import com.ce.scrm.center.service.enums.AssignCustSourceSpecialEnum;
import com.ce.scrm.center.service.enums.ProtectStateEnum;
import com.ce.scrm.center.support.mq.RocketMqOperate;
import com.ce.scrm.center.web.entity.dto.openapi.WebsiteIncludeWebDto;
import com.ce.scrm.center.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.center.web.entity.response.WebResult;
import com.ce.scrm.center.web.util.CustomKeyAES;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ce.scrm.center.service.constant.ServiceConstant.MqConstant.Topic.SCRM_WEBSITE_INCLUDE_TOPIC;

/**
 * @version 1.0
 * @Description: 中企 开放平台
 * @Author: lijinpeng
 * @Date: 2025/1/22 15:21
 */
@Slf4j
@RestController
@RequestMapping("/openapi/ce")
public class OpenapiCeController {

    @Resource
    private CirculationLossBusiness circulationLossBusiness;

    @Resource
    private EmployeeInfoBusiness employeeInfoBusiness;

    @Resource
    private CmCustProtectService cmCustProtectService;

    @DubboReference
    private BusinessAppService businessAppService;

    @Autowired
    private RocketMqOperate rocketMqOperate;

    @Value("${auth.login.cesupport.secret}")
    private String ceSecretKey;

    /*
     * @Description 商务角色：老客待流转  客户数量
     * <AUTHOR>
     * @date 2025/2/6 09:51
     * @param signEmpId
     * @return cn.ce.cesupport.base.vo.ScrmResult<java.lang.Long>
     */
    @GetMapping("getCirculationCount")
    public ScrmResult<Long> getCirculationCount(@RequestParam(value = "signEmpId") String signEmpId) {
        String empId = decrypt(signEmpId);
        if (StringUtils.isEmpty(empId)) {
            return ScrmResult.error(CodeMessageEnum.NOT_LOGIN);
        }
        EmployeeInfoBusinessDto employeeInfoByEmpId = employeeInfoBusiness.getEmployeeInfoByEmpId(empId);
        if (employeeInfoByEmpId == null || StringUtils.isEmpty(employeeInfoByEmpId.getId())) {
            return ScrmResult.error(CodeMessageEnum.NOT_LOGIN);
        }
        long result = 0L;
        if (PositionUtil.isBusinessSaler(employeeInfoByEmpId.getPosition())) {
            CirculationLossPageBusinessDto businessDto = new CirculationLossPageBusinessDto();
            businessDto.setPageNum(1);
            businessDto.setPageSize(1);
            businessDto.setOrigin(AssignCustSourceSpecialEnum.CIRCULATION.getValue());
            businessDto.setAreaId(employeeInfoByEmpId.getAreaId());
            businessDto.setSubId(employeeInfoByEmpId.getSubId());
            businessDto.setDeptId(employeeInfoByEmpId.getOrgId());
            businessDto.setSalerId(employeeInfoByEmpId.getId());
            Page<CirculationLossPageBusinessView> pageForReleaseReason = circulationLossBusiness.getCirculationList(businessDto);
            result = pageForReleaseReason.getTotal();
        }else {
            return ScrmResult.error(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }
        return ScrmResult.success(result);
    }

    /*
     * @Description 商务角色：保护中--商机客户，没打回执的客户数
     * <AUTHOR>
     * @date 2025/2/6 10:36
     * @param signEmpId
     * @return cn.ce.cesupport.base.vo.ScrmResult<java.lang.Long>
     */
    @GetMapping("getProtectBusinessOpportunityNotReceiptCount")
    public ScrmResult<Long> getProtectBusinessOpportunityNotReceiptCount(@RequestParam(value = "signEmpId") String signEmpId) {
        String empId = decrypt(signEmpId);
        if (StringUtils.isEmpty(empId)) {
            return ScrmResult.error(CodeMessageEnum.NOT_LOGIN);
        }
        EmployeeInfoBusinessDto employeeInfoByEmpId = employeeInfoBusiness.getEmployeeInfoByEmpId(empId);
        if (employeeInfoByEmpId == null || StringUtils.isEmpty(employeeInfoByEmpId.getId())) {
            return ScrmResult.error(CodeMessageEnum.NOT_LOGIN);
        }
        long result = 0L;
        if (PositionUtil.isBusinessSaler(employeeInfoByEmpId.getPosition())) {
            // 保护中-商机客户-没打回执的客户数
            List<CmCustProtect> list = cmCustProtectService.lambdaQuery()
                    .eq(CmCustProtect::getStatus, ProtectStateEnum.PROTECT.getState())
                    .eq(CmCustProtect::getSalerId, empId)
                    .isNotNull(CmCustProtect::getBusioppoCode)
                    .list();
            Set<String> busioppoCodeSet = list.stream().map(CmCustProtect::getBusioppoCode).collect(Collectors.toSet());
            result = businessAppService.getNotReceiptByBusiOppoCodeCount(new ArrayList<>(busioppoCodeSet));
        }else {
            return ScrmResult.error(CodeMessageEnum.REQUEST_POSITION_NOT_MATCH);
        }
        return ScrmResult.success(result);
    }

    private String decrypt(String signUserId){
        if (StringUtils.isBlank(signUserId)){
            return null;
        }
        try {
            return CustomKeyAES.decrypt(signUserId, ceSecretKey);
        }catch (Exception e){
            log.error("跨境用户解密失败,signUserId={}",signUserId);
        }
        return null;
    }

    /***
     * 网站收录数
     * @param websiteIncludeWebDto
     * <AUTHOR>
     * @date 2025/8/12 16:50
     * @version 1.0.0
     * @return com.ce.scrm.center.web.entity.response.WebResult<java.lang.Boolean>
    **/
    @PostMapping("/websiteInclude")
    public WebResult<Boolean> websiteInclude(@RequestBody WebsiteIncludeWebDto websiteIncludeWebDto) {
        log.info("业务平台推送网站收录参数:{}", JSONObject.toJSONString(websiteIncludeWebDto));
        if (websiteIncludeWebDto == null || StringUtils.isEmpty(websiteIncludeWebDto.getSecurityKey())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        if (!Objects.equals(websiteIncludeWebDto.getSecurityKey(),ceSecretKey)){
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        if (websiteIncludeWebDto == null || StringUtils.isEmpty(websiteIncludeWebDto.getInstanceCode())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        rocketMqOperate.syncSend(SCRM_WEBSITE_INCLUDE_TOPIC, JSONObject.toJSONString(websiteIncludeWebDto));
        return WebResult.success(true);
    }
}
