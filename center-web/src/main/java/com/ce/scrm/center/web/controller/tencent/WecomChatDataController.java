package com.ce.scrm.center.web.controller.tencent;

import com.ce.scrm.center.web.controller.tencent.aes.WXBizJsonMsgCrypt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/17
 */
@Slf4j
@RestController
@RequestMapping("/wecom/chatdata")
public class WecomChatDataController {

    /**
     * 企业ID
     */
    @Value("${wecom.event.corpId:}")
    private String corpId;

    /**
     * 事件设置的token
     */
    @Value("${wecom.event.token:}")
    private String eventToken;

    /**
     * 事件设置的aes解密key
     */
    @Value("${wecom.event.aesKey:}")
    private String eventAesKey;


    @Autowired
    private WecomChatDataService wecomChatDataService;




    /**
     * 回调url check
     * 企微会调用这个接口校验这个接口是否可用
     * @param msg_signature
     * @param timestamp
     * @param nonce
     * @param echostr
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/3/22 15:29
     * @version 1.0.0
     **/
    @GetMapping("/push/event")
    public String approvalEventCheckForJiTuan(String msg_signature, String timestamp, String nonce, String echostr) throws Exception {
        log.info("msg_signature:{} timestamp:{} nonce:{} echostr:{}",msg_signature, timestamp, nonce, echostr);
        WXBizJsonMsgCrypt wxBizJsonMsgCrypt = new WXBizJsonMsgCrypt(eventToken, eventAesKey,corpId);
        return wxBizJsonMsgCrypt.VerifyURL(msg_signature, timestamp, nonce, echostr);
    }

    /**
     * 这个才是真正接收企微事件的接口
     * @param msg_signature
     * @param timestamp
     * @param nonce
     * @param xmldata
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/3/22 15:30
     * @version 1.0.0
     **/
    @PostMapping("/push/event")
    public String approvalEventForJiTuan(String msg_signature, String timestamp, String nonce, @RequestBody String xmldata) throws Exception {
        log.info("msg_signature:{} timestamp:{} nonce:{}",msg_signature, timestamp, nonce);
        wecomChatDataService.event(msg_signature, timestamp, nonce, xmldata);
        return "ok";
    }
}
