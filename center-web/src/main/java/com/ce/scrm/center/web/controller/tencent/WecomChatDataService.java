package com.ce.scrm.center.web.controller.tencent;

import cn.hutool.json.JSONObject;
import cn.hutool.json.XML;
import com.ce.scrm.center.web.controller.tencent.aes.WXBizJsonMsgCrypt;
import com.ce.scrm.center.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/17
 */
@Slf4j
@Component
public class WecomChatDataService {

    /**
     * 企业ID
     */
    @Value("${wecom.event.corpId:}")
    private String corpId;

    /**
     * 事件设置的token
     */
    @Value("${wecom.event.token:}")
    private String eventToken;

    /**
     * 事件设置的aes解密key
     */
    @Value("${wecom.event.aesKey:}")
    private String eventAesKey;

    public WebResult<Boolean> event(String msgSignature, String timestamp, String nonce, String xmldata) {
        log.info("msg_signature:{} timestamp:{} nonce:{} echostr:{}", msgSignature, timestamp, nonce, xmldata);
        try {
            JSONObject xmlJsonObject = XML.toJSONObject(xmldata).getJSONObject("xml");
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("tousername", xmlJsonObject.getStr("ToUserName"));
            jsonObject.set("encrypt", xmlJsonObject.getStr("Encrypt"));
            jsonObject.set("agentid", xmlJsonObject.getStr("AgentID"));
            log.info("jsonObject:{}", jsonObject);
            WXBizJsonMsgCrypt wxBizJsonMsgCrypt = new WXBizJsonMsgCrypt(eventToken, eventAesKey, corpId);
            String sMsg = wxBizJsonMsgCrypt.DecryptMsg(msgSignature, timestamp, nonce, jsonObject.toString());
            log.info("sMsg:{}", sMsg);
            JSONObject dataXmlJson = XML.toJSONObject(sMsg).getJSONObject("xml");
            log.info("dataXmlJson:{}", dataXmlJson);
        } catch (Exception exception) {
            log.error("approvalEvent fail", exception);
        }
        return WebResult.success();
    }
}
