package com.ce.scrm.center.web.entity.response;

/**
 * 返回码枚举类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/28 下午3:39
 */
public enum WebCodeMessageEnum {
    //成功返回码
    REQUEST_SUCCESS("200", "请求成功"),
    //会员登录异常，特殊处理，兼容之前的校验码
    //异常返回码，从100001开始
    SERVER_INTERNAL_EXCEPTION("100001", "网络超时，请稍后重试哦～"),
    REQUEST_METHOD_EXCEPTION("100002", "请求方式异常"),
    REQUEST_ADDRESS_EXCEPTION("100003", "请求地址不存在"),
    REQUEST_PARAM_EXCEPTION("100004", "请求参数异常"),
    RPC_EXCEPTION("100005", "网络连接超时，请稍后重试哦～"),
    REQUEST_PARAM_NOT_NULL("100006", "请求参数不能为空"),
    REQUEST_DUBBO_GENERIC_PASSWORD_ERROR("100007", "请确认当前接口的正确性"),
    NOT_LOGIN("100008", "登录超时"),
    REQUEST_POSITION_NOT_MATCH("100009", "无权操作"),
    DATA_NOT_EXIST("100010", "数据不存在"),
    CUSTOMER_CIRCULATION_SETTING_EDIT_LESS_THAN_ONE_YEAR("100011", "每年只可以修改一次规则"),
    NOT_CLOSING_CUSTOMER("100012", "该客户非成交客户，不能添加"),
    NOT_CURRENT_COMPANY_PROTECT("100013", "该客户未由您的分司保护，不能添加"),
    DATA_EXIST("100015", "数据已存在，请勿重复添加"),
    SIGN_ERROR("100016", "签名为空或者错误"),
    REQUEST_PARAM_NAME_NULL("100017", "客户名称为空"),
    REQUEST_PARAM_CUSTOMER_TYPE_NULL("100018", "客户类型为空"),
    REQUEST_PARAM_CERTIFICATE_TYPE_NULL("100019", "个人客户证件类型为空"),
    REQUEST_PARAM_CERTIFICATE_CODE_NULL("100020", "个人客户证件号码为空"),
    REQUEST_PARAM_COMPANY_NAME_ERROR("100021", "企业客户名称格式错误"),
    REQUEST_PARAM_CREATOR_NULL("100022", "创建人为空"),
    REQUEST_PARAM_PID_NULL("100023", "国内企业pid不能为空"),
    ADD_CUSTOMER_FAIL("100024", "添加客户失败"),
    REQUEST_PARAM_CUSTOMER_TYPE_ERROR("100025", "客户类型非法"),
    REQUEST_PARAM_CUSTOMER_ID_NULL("100026", "客户id为空"),
    REQUEST_PARAM_CONTACT_PERSON_NAME_NULL("100027", "联系人姓名为空"),
    REQUEST_PARAM_GENDER_NULL("100028", "联系人性别为空"),
    REQUEST_PARAM_POSITION_NULL("100029", "联系人职位为空"),
    REQUEST_PARAM_PHONE_NULL("100030", "联系人手机号为空"),
    ADD_CONTACT_FAIL("100031", "添加联系人失败"),
    WX_LOGIN_EXPIRED("403", "登录过期或者鉴权未通过"),
    //历史线索导入失败
    CUSTOMER_LEADS_IMPORT_FAIL("100200", "导入失败"),
    //Leads导入失败
    CUSTOMER_LEADS_CREATE_FAIL("100201", "Leads创建失败"),
    //创建客户失败，添加leads失败
    CUSTOMER_CREATE_FAIL("100203", "创建leads失败，请检查客户名称"),
    //查询通话记录失败
    CUSTOMER_LEADS_CALL_RECORDS_FAIL("100202", "查询通话记录失败"),

    CUSTOMER_LEADS_DATASOURCE_EMPTY("100100","Leads数据来源不能为空"),
    CUSTOMER_LEADS_CUSTOMERID_EMPTY("100101","Leads客户不能为空"),
    CUSTOMER_LEADS_TYPE_EMPTY("100102","Leads客户不能为空"),
    CUSTOMER_LEADS_CODE_EMPTY("100103","LeadsCode不能为空"),
    CUSTOMER_LEADS_SOURCE_EMPTY("100104","Leads来源不能为空"),
    CUSTOMER_LEADS_CUSTOMER_ADD_FAIL("100105","Leads创建客户失败"),
    CUSTOMER_LEADS_CONTACT_ADD_FAIL("100105","Leads创建联系人失败"),
    //渠道不能为空
    CUSTOMER_LEADS_CHANNEL_EMPTY("100106","Leads渠道不能为空"),
    //活动不能为空
    CUSTOMER_LEADS_ACTIVITY_EMPTY("100107","Leads活动不能为空"),
    //端口不能为空
    CUSTOMER_LEADS_CLIENT_TYPE_EMPTY("100108","Leads端口不能为空"),
    //保存leads失败
    CUSTOMER_LEADS_SAVE_FAIL("100109","Leads保存失败"),
    //保护成功
    CUSTOMER_LEADS_PROTECT_SUCCESS("100110","leads保存成功"),

    SEGMENT_NOT_EXIST("100400","分群不存在"),

    SEGMENT_DISTRIBUTED("100401","分群重复下发");



    private final String code;
    private final String msg;

    WebCodeMessageEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
