package com.ce.scrm.center.web.entity.response;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.center.service.business.entity.response.BusinessResult;
import com.ce.scrm.center.web.entity.dto.marketingsms.Result;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;

/**
 * 返回包装体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/28 下午3:38
 */
@Data
public class WebResult<T> implements Serializable {
    private static final long serialVersionUID = -2156057946333564975L;
    private String traceId;
    private String msg;
    private String code;
    private T data;

    private WebResult() {
    }

    private WebResult(T data) {
        this.code = WebCodeMessageEnum.REQUEST_SUCCESS.getCode();
        this.msg = WebCodeMessageEnum.REQUEST_SUCCESS.getMsg();
        this.data = data;
    }

    private WebResult(T data, String msg) {
        this.code = WebCodeMessageEnum.REQUEST_SUCCESS.getCode();
        this.msg = msg;
        this.data = data;
    }

    private WebResult(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private WebResult(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    private WebResult(WebCodeMessageEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMsg();
        }
    }

    public static <T> WebResult<T> success(T data) {
        return new WebResult<>(data);
    }

    public static <T> WebResult<T> success(T data, String msg) {
        return new WebResult<>(data, msg);
    }

    public static <T> WebResult<T> success() {
        return success(null);
    }

    public static <T> WebResult<T> error(WebCodeMessageEnum cm) {
        return new WebResult<>(cm);
    }

    public static <T> WebResult<T> error(WebCodeMessageEnum cm, String msg) {
        return new WebResult<>(cm.getCode(), msg);
    }

    public static <T> WebResult<T> error(String code, String msg) {
        return new WebResult<>(code, msg);
    }

    public static <T> WebResult<T> error(WebResult<?> resultEntity) {
        return new WebResult<>(resultEntity.getCode(), resultEntity.getMsg());
    }

    public Boolean checkSuccess() {
        return WebCodeMessageEnum.REQUEST_SUCCESS.getCode().equals(this.code);
    }

    public static <T> WebResult<T> convertBusinessResult(BusinessResult<T> businessResult) {
        return new WebResult<>(businessResult.getCode(), businessResult.getMsg(), businessResult.getData());
    }

    public static <T> WebResult<WebPageInfo<T>> convertBusinessPageResult(BusinessResult<Page<T>> businessResult) {
        WebPageInfo<T> pageInfo = new WebPageInfo<>();
        if (businessResult == null || businessResult.getData() == null || CollectionUtils.isEmpty(businessResult.getData().getRecords())) {
            pageInfo.setList(Collections.emptyList());
            pageInfo.setTotal(0L);
            return WebResult.success(pageInfo);
        }
        pageInfo.setList(businessResult.getData().getRecords());
        pageInfo.setTotal(businessResult.getData().getTotal());
        return new WebResult<>(businessResult.getCode(), businessResult.getMsg(), pageInfo);
    }

	public static <T> WebResult<T> convertKjResultWebResult(Result<T> result) {
		return new WebResult<>(result.getCode(), result.getMessage(), result.getResult());
	}
}
