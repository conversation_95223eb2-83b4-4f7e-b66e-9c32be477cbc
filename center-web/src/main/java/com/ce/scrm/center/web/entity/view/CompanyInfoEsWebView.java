package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 公司数据(ES)
 * <AUTHOR>
 * @date 2024/1/19 14:31
 * @version 1.0.0
 */
@Data
public class CompanyInfoEsWebView implements Serializable {
    /**
     * 搜客宝公司唯一ID
     */
    private String pid;
    /**
     * 社会信用代码
     */
    private String uncid;
    /**
     * 客户名称
     */
    private String entName;
    /**
     * ka标记
     */
    private Integer kaFlag;

    /**
     * 所属行业一级
     */
    private String firstIndustry;

    /**
     * 所属行业二级
     */
    private List<String> secondIndustry;

    /**
     * 所属行业三级
     */
    private String thirdIndustry;

    /**
     * 所属行业四级
     */
    private String fourthIndustry;

    /**
     * 注册资金
     */
    private String regCapUnify;

    /**
     * 成立日期
     */
    private Date esDate;

    /**
     * 地理坐标
     */
    private String location;

    /**
     * 通讯地址
     */
    private String contactAddress;

    /**
     * 省code
     */
    private String province;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市code
     */
    private String city;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区code
     */
    private String district;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 注册地址
     */
    private String regAddress;

    /**
     * 历史名称
     */
    private List<String> historyName;

    /**
     * 是否为一般纳税人
     */
    private Boolean isGeneralTaxpayer;
    /**
     * 是否为A级纳税人
     */
    private Boolean hasATaxCredit;
    /**
     * 有无招投标
     */
    private Boolean hasTender;
    /**
     * 有无进出口信用
     */
    private Boolean hasImportAndExportCredit;
    /**
     * 有无参展
     */
    private Boolean hasExhJournal;
    /**
     * 高新企业
     */
    private Boolean isHighTech;
    /**
     * 有无商标
     */
    private Boolean hasTrademark;
    /**
     * 有无专利
     */
    private Boolean hasPatent;
    /**
     * 有无推广
     */
    private Boolean hasSem;

    /**
     * 上市公司
     */
    private String flag12;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    private List<String> flag7;

    /**
     * 搜客宝 律师,学校
     */
    private List<String> flag8;

    /**
     * 搜客宝 科技型企业
     */
    private List<String> techTypeCompany;

    /**
     * 员工人数
     */
    private String staffs;

    /**
     * 有无域名备案
     */
    private Boolean hasIcp;

    /**
     * 电商上架平台：搜客宝数据类型不统一，有2种格式："[4, 7]"，[4, 7]
     */
    private List<String> ecomShopPlatform;

    /**
     * 涵盖证书类型
     */
    private List<String> certL1Type;

    /**
     * 主营产品
     */
    private String b2bProduct;

    /**
     * 新兴行业
     */
    private List<String> firstEmergingIndustry;
}