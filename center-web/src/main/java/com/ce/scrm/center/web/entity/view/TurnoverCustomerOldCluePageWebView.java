package com.ce.scrm.center.web.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TurnoverCustomerOldCluePageWebView implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	private String custId;

	/**
	 * 客户阶段
	 */
	private Integer followOrNot;

	/**
	 * 客户阶段
	 */
	private Integer visitOrNot;

	private Date templateIssuedExceedTime;
	private Date templateIssuedSuccessTime;

	private String templateIssuedExceedTimeStr;
	private String templateIssuedSuccessTimeStr;
	/**
	 * 客户名称
	 */
	private String custName;

	/**
	 * 商务代表ID
	 */
	private String salerId;

	/**
	 * 部门ID
	 */
	private String bussdeptId;

	/**
	 * 分公司ID
	 */
	private String subcompanyId;

	/**
	 * 区域ID
	 */
	private String areaId;

	/**
	 * 商务代表ID
	 */
	private String salerName;

	/**
	 * 部门ID
	 */
	private String deptName;

	/**
	 * 分公司ID
	 */
	private String subName;

	/**
	 * 区域ID
	 */
	private String areaName;

	/**
	 * 模板ID
	 */
	private String templateId;
	/**
	 * 模板名称
	 */
	private String templateName;
	/**
	 * 模板类型
	 */
	private Integer templateType;

	/**
	 * 是否已过期
	 */
	private Integer isExceed;

	/**
	 * 是否已过期
	 */
	private String isExceedStr;

	/**
	 * 是否跟进
	 */
	private String followOrNotStr;

	/**
	 * 是否拜访
	 */
	private String visitOrNotStr;

	/**
	 * 拜访有效期
	 */
	private Date clueFollowExpirationDate;
	/**
	 * 拜访有效期
	 */
	private String clueFollowExpirationDateStr;

	/**
	 * 销售阶段
	 */
	private String salesStageStr;

	/**
	 * 预计成交金额
	 */
	private Double expectDealAmount;

	/**
	 * 预计成交金额
	 */
	private String expectDealAmountStr;

	/**
	 * 多个模板ID，逗号分隔
	 */
	private String templateIds;

	/**
	 * 是否是曾用名 true是 false否
	 */
	private Boolean historyNameFlag;
}